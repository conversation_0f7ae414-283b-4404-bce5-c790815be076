<!--pages/index/index.wxml-->
<view class="page-container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input" bindtap="onSearchTap">
      <text class="search-placeholder">搜索菜品名称或食材</text>
      <text class="search-icon">🔍</text>
    </view>
  </view>

  <!-- 分类导航 -->
  <view class="category-nav">
    <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="{{false}}">
      <view class="category-list">
        <view 
          class="category-item {{selectedCategory === item.id ? 'active' : ''}}"
          wx:for="{{categories}}" 
          wx:key="id"
          bindtap="onCategoryTap"
          data-id="{{item.id}}"
        >
          <text class="category-icon">{{item.icon}}</text>
          <text class="category-name">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 今日推荐 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">今日推荐</text>
      <text class="section-more" bindtap="onViewMoreTap" data-type="recommended">查看更多</text>
    </view>
    <view class="dish-grid">
      <view 
        class="dish-card"
        wx:for="{{recommendedDishes}}" 
        wx:key="_id"
        bindtap="onDishTap"
        data-id="{{item._id}}"
      >
        <image class="dish-image" src="{{item.image}}" mode="aspectFill" lazy-load="true"></image>
        <view class="dish-info">
          <text class="dish-name">{{item.name}}</text>
          <view class="dish-meta">
            <text class="dish-time">{{item.cookTime}}分钟</text>
            <text class="dish-difficulty">{{item.difficultyText}}</text>
          </view>
          <view class="dish-rating">
            <text class="rating-stars">{{item.ratingStars}}</text>
            <text class="rating-score">{{item.rating}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 热门菜品 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">热门菜品</text>
      <text class="section-more" bindtap="onViewMoreTap" data-type="popular">查看更多</text>
    </view>
    <view class="dish-list">
      <view 
        class="dish-item"
        wx:for="{{popularDishes}}" 
        wx:key="_id"
        bindtap="onDishTap"
        data-id="{{item._id}}"
      >
        <image class="dish-thumb" src="{{item.image}}" mode="aspectFill" lazy-load="true"></image>
        <view class="dish-content">
          <text class="dish-title">{{item.name}}</text>
          <text class="dish-desc">{{item.description}}</text>
          <view class="dish-stats">
            <text class="stat-item">⏱️ {{item.cookTime}}分钟</text>
            <text class="stat-item">⭐ {{item.rating}}</text>
            <text class="stat-item">👥 {{item.orderCount}}人点过</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 最近添加 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">最近添加</text>
      <text class="section-more" bindtap="onViewMoreTap" data-type="recent">查看更多</text>
    </view>
    <view class="dish-grid">
      <view 
        class="dish-card"
        wx:for="{{recentDishes}}" 
        wx:key="_id"
        bindtap="onDishTap"
        data-id="{{item._id}}"
      >
        <image class="dish-image" src="{{item.image}}" mode="aspectFill" lazy-load="true"></image>
        <view class="dish-info">
          <text class="dish-name">{{item.name}}</text>
          <view class="dish-meta">
            <text class="dish-time">{{item.cookTime}}分钟</text>
            <text class="dish-difficulty">{{item.difficultyText}}</text>
          </view>
          <text class="dish-date">{{item.createTimeText}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="loading-more" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{isEmpty}}">
    <text class="empty-icon">🍽️</text>
    <text class="empty-text">暂无菜品数据</text>
    <button class="btn btn-primary" bindtap="onRefreshTap">刷新重试</button>
  </view>
</view>
