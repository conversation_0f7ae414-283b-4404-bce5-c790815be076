// app.js
App({
  onLaunch() {
    console.log('家庭点餐小程序启动');

    // 检查登录状态
    this.checkLoginStatus();

    // 获取系统信息
    this.getSystemInfo();
  },

  onShow() {
    // 小程序显示时的处理
  },

  onHide() {
    // 小程序隐藏时的处理
  },

  onError(error) {
    // 小程序错误处理
    console.error('小程序发生错误:', error);
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.globalData.userInfo = userInfo;
      this.globalData.isLoggedIn = true;
    } else {
      this.globalData.isLoggedIn = false;
    }
  },

  // 用户登录
  login() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          const userInfo = res.userInfo;

          // 使用本地API进行登录
          const { userAPI } = require('./utils/api');
          userAPI.login(userInfo).then((userData) => {
            this.globalData.userInfo = userData;
            this.globalData.isLoggedIn = true;
            resolve(userData);
          }).catch((error) => {
            reject(error);
          });
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  },

  // 用户登出
  logout() {
    this.globalData.userInfo = null;
    this.globalData.isLoggedIn = false;
    wx.removeStorageSync('userInfo');
  },

  // 显示加载提示
  showLoading(title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    });
  },

  // 隐藏加载提示
  hideLoading() {
    wx.hideLoading();
  },

  // 显示成功提示
  showSuccess(title) {
    wx.showToast({
      title: title,
      icon: 'success',
      duration: 2000
    });
  },

  // 显示错误提示
  showError(title) {
    wx.showToast({
      title: title,
      icon: 'error',
      duration: 2000
    });
  },

  // 显示普通提示
  showToast(title) {
    wx.showToast({
      title: title,
      icon: 'none',
      duration: 2000
    });
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res;
        console.log('系统信息:', res);
      },
      fail: (error) => {
        console.error('获取系统信息失败:', error);
      }
    });
  },

  // 全局数据
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    systemInfo: null
  }
});
