# 数据库设置指南

## 概述

本文档介绍如何设置和初始化家庭点餐小程序的云数据库。

## 数据库结构

### 1. 菜品表 (dishes)

存储所有菜品信息，包括基本信息、制作步骤、营养信息等。

**字段说明：**
- `_id`: 菜品唯一标识
- `name`: 菜品名称
- `category`: 菜品分类（川菜、粤菜等）
- `image`: 菜品图片URL
- `description`: 菜品描述
- `difficulty`: 难度等级（1-5）
- `cookTime`: 制作时间（分钟）
- `rating`: 平均评分
- `orderCount`: 点餐次数
- `ingredients`: 食材清单数组
- `steps`: 制作步骤数组
- `tags`: 标签数组
- `nutrition`: 营养信息对象
- `createTime`: 创建时间
- `updateTime`: 更新时间

### 2. 用户表 (users)

存储用户基本信息和偏好设置。

**字段说明：**
- `_id`: 用户唯一标识
- `openid`: 微信openid
- `nickname`: 用户昵称
- `avatar`: 头像URL
- `familyId`: 家庭ID（可选）
- `role`: 用户角色（member/admin）
- `preferences`: 偏好设置对象
- `createTime`: 创建时间
- `lastLoginTime`: 最后登录时间

### 3. 点餐记录表 (orders)

存储用户的点餐记录。

**字段说明：**
- `_id`: 记录唯一标识
- `userId`: 用户ID
- `dishId`: 菜品ID
- `dishName`: 菜品名称
- `dishImage`: 菜品图片
- `cookTime`: 制作时间
- `difficulty`: 难度等级
- `note`: 备注
- `status`: 状态（pending/cooking/completed）
- `orderTime`: 点餐时间
- `rating`: 用户评分
- `comment`: 评价内容

### 4. 收藏表 (favorites)

存储用户收藏的菜品。

**字段说明：**
- `_id`: 收藏记录唯一标识
- `userId`: 用户ID
- `dishId`: 菜品ID
- `createTime`: 收藏时间

## 云函数说明

### 1. initDatabase
**功能：** 初始化数据库，创建集合和示例数据
**调用方式：** 手动调用一次即可

### 2. getDishes
**功能：** 获取菜品列表，支持分类、搜索、筛选、排序
**参数：**
- `category`: 分类筛选
- `page`: 页码
- `limit`: 每页数量
- `sortBy`: 排序方式
- `keyword`: 搜索关键词
- `difficulty`: 难度筛选
- `cookTimeRange`: 时间范围筛选

### 3. getDishDetail
**功能：** 获取菜品详情
**参数：**
- `dishId`: 菜品ID

### 4. login
**功能：** 用户登录，创建或更新用户信息
**参数：**
- `userInfo`: 用户信息对象

### 5. createOrder
**功能：** 创建点餐记录
**参数：**
- `dishId`: 菜品ID
- `note`: 备注（可选）

### 6. getRecommendedDishes
**功能：** 获取推荐菜品，支持个性化推荐
**参数：**
- `limit`: 推荐数量

## 初始化步骤

### 1. 创建云开发环境

1. 在微信开发者工具中打开项目
2. 点击"云开发"按钮
3. 创建云开发环境
4. 记录环境ID

### 2. 配置环境ID

在以下文件中配置云开发环境ID：

**miniprogram/app.js:**
```javascript
wx.cloud.init({
  env: 'your-cloud-env-id', // 替换为实际环境ID
  traceUser: true,
});
```

**miniprogram/utils/constants.js:**
```javascript
const APP_CONFIG = {
  CLOUD_ENV_ID: 'your-cloud-env-id', // 替换为实际环境ID
  // ...
};
```

### 3. 部署云函数

1. 在微信开发者工具中，右键点击 `cloudfunctions` 文件夹
2. 选择"当前环境"为你创建的环境
3. 逐个右键点击每个云函数文件夹，选择"上传并部署：云端安装依赖"

需要部署的云函数：
- `initDatabase`
- `getDishes`
- `getDishDetail`
- `login`
- `createOrder`
- `getRecommendedDishes`

### 4. 初始化数据库

1. 在微信开发者工具的"云开发"控制台中
2. 进入"云函数"页面
3. 找到 `initDatabase` 函数
4. 点击"测试"按钮执行初始化

或者在小程序中调用：
```javascript
wx.cloud.callFunction({
  name: 'initDatabase',
  success: (res) => {
    console.log('数据库初始化成功:', res);
  },
  fail: (error) => {
    console.error('数据库初始化失败:', error);
  }
});
```

### 5. 验证初始化结果

1. 在云开发控制台的"数据库"页面
2. 检查是否创建了以下集合：
   - `dishes`（包含5条示例菜品）
   - `users`（空集合）
   - `orders`（空集合）
   - `favorites`（空集合）

## 数据库权限设置

为了安全起见，建议设置以下权限规则：

### dishes 集合
- 读取：所有用户
- 写入：仅管理员

### users 集合
- 读取：仅创建者
- 写入：仅创建者

### orders 集合
- 读取：仅创建者
- 写入：仅创建者

### favorites 集合
- 读取：仅创建者
- 写入：仅创建者

## 索引优化

系统会自动创建以下索引以提高查询性能：

**dishes 集合索引：**
- `category`: 1（分类查询）
- `rating`: -1（评分排序）
- `orderCount`: -1（热度排序）
- `createTime`: -1（时间排序）

## 注意事项

1. **环境ID配置**：确保所有地方的环境ID配置一致
2. **权限设置**：根据实际需求调整数据库权限
3. **数据备份**：定期备份重要数据
4. **性能监控**：关注云函数执行时间和数据库查询性能
5. **成本控制**：监控云开发资源使用情况

## 故障排除

### 常见问题

1. **云函数调用失败**
   - 检查环境ID配置
   - 确认云函数已正确部署
   - 查看云函数日志

2. **数据库连接失败**
   - 检查云开发环境状态
   - 确认权限设置正确

3. **数据查询为空**
   - 确认数据库初始化成功
   - 检查查询条件是否正确

### 调试方法

1. 使用云开发控制台查看云函数日志
2. 在小程序中使用 `console.log` 输出调试信息
3. 使用云开发控制台直接测试云函数
