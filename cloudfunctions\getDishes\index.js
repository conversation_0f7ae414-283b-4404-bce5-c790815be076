// 云函数：获取菜品列表
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  try {
    const { 
      category = 'all', 
      page = 1, 
      limit = 20, 
      sortBy = 'createTime',
      keyword = '',
      difficulty = null,
      cookTimeRange = null
    } = event;

    // 构建查询条件
    let query = {};
    
    // 分类筛选
    if (category && category !== 'all') {
      query.category = category;
    }
    
    // 关键词搜索
    if (keyword) {
      query.name = db.RegExp({
        regexp: keyword,
        options: 'i'
      });
    }
    
    // 难度筛选
    if (difficulty) {
      if (difficulty.includes('-')) {
        const [min, max] = difficulty.split('-').map(Number);
        query.difficulty = _.gte(min).and(_.lte(max));
      } else {
        query.difficulty = Number(difficulty);
      }
    }
    
    // 制作时间筛选
    if (cookTimeRange) {
      if (cookTimeRange === '0-30') {
        query.cookTime = _.lte(30);
      } else if (cookTimeRange === '30-60') {
        query.cookTime = _.gte(30).and(_.lte(60));
      } else if (cookTimeRange === '60-120') {
        query.cookTime = _.gte(60).and(_.lte(120));
      } else if (cookTimeRange === '120+') {
        query.cookTime = _.gte(120);
      }
    }

    // 构建排序条件
    let orderBy = {};
    switch (sortBy) {
      case 'rating':
        orderBy.rating = 'desc';
        break;
      case 'orderCount':
        orderBy.orderCount = 'desc';
        break;
      case 'cookTime':
        orderBy.cookTime = 'asc';
        break;
      case 'difficulty':
        orderBy.difficulty = 'asc';
        break;
      case 'createTime':
      default:
        orderBy.createTime = 'desc';
        break;
    }

    // 计算跳过的记录数
    const skip = (page - 1) * limit;

    // 查询数据
    const result = await db.collection('dishes')
      .where(query)
      .orderBy(Object.keys(orderBy)[0], Object.values(orderBy)[0])
      .skip(skip)
      .limit(limit)
      .get();

    // 获取总数
    const countResult = await db.collection('dishes')
      .where(query)
      .count();

    const total = countResult.total;
    const hasMore = skip + result.data.length < total;

    return {
      code: 0,
      message: '获取菜品列表成功',
      data: {
        dishes: result.data,
        total: total,
        page: page,
        limit: limit,
        hasMore: hasMore
      }
    };

  } catch (error) {
    console.error('获取菜品列表失败:', error);
    return {
      code: -1,
      message: '获取菜品列表失败',
      error: error.message
    };
  }
};
