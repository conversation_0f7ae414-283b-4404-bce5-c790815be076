// utils/api.js - API请求工具类（本地数据版本）

const { dishes } = require('../data/dishes');
const { users, currentUser } = require('../data/users');
const { orders } = require('../data/orders');
const { favorites } = require('../data/favorites');
const { generateId } = require('./util');

/**
 * 模拟异步请求
 * @param {any} data 返回的数据
 * @param {number} delay 延迟时间
 * @returns {Promise} 返回Promise对象
 */
function mockRequest(data, delay = 300) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(data);
    }, delay);
  });
}

/**
 * 菜品相关API
 */
const dishAPI = {
  // 获取菜品列表
  getDishes(params = {}) {
    const {
      category = 'all',
      page = 1,
      limit = 20,
      sortBy = 'createTime',
      keyword = '',
      difficulty = null,
      cookTimeRange = null
    } = params;

    let filteredDishes = [...dishes];

    // 分类筛选
    if (category && category !== 'all') {
      filteredDishes = filteredDishes.filter(dish => dish.category === category);
    }

    // 关键词搜索
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase();
      filteredDishes = filteredDishes.filter(dish =>
        dish.name.toLowerCase().includes(lowerKeyword) ||
        dish.description.toLowerCase().includes(lowerKeyword) ||
        dish.tags.some(tag => tag.toLowerCase().includes(lowerKeyword))
      );
    }

    // 难度筛选
    if (difficulty) {
      if (difficulty.includes('-')) {
        const [min, max] = difficulty.split('-').map(Number);
        filteredDishes = filteredDishes.filter(dish =>
          dish.difficulty >= min && dish.difficulty <= max
        );
      } else {
        filteredDishes = filteredDishes.filter(dish =>
          dish.difficulty === Number(difficulty)
        );
      }
    }

    // 制作时间筛选
    if (cookTimeRange) {
      if (cookTimeRange === '0-30') {
        filteredDishes = filteredDishes.filter(dish => dish.cookTime <= 30);
      } else if (cookTimeRange === '30-60') {
        filteredDishes = filteredDishes.filter(dish =>
          dish.cookTime > 30 && dish.cookTime <= 60
        );
      } else if (cookTimeRange === '60-120') {
        filteredDishes = filteredDishes.filter(dish =>
          dish.cookTime > 60 && dish.cookTime <= 120
        );
      } else if (cookTimeRange === '120+') {
        filteredDishes = filteredDishes.filter(dish => dish.cookTime > 120);
      }
    }

    // 排序
    filteredDishes.sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'orderCount':
          return b.orderCount - a.orderCount;
        case 'cookTime':
          return a.cookTime - b.cookTime;
        case 'difficulty':
          return a.difficulty - b.difficulty;
        case 'createTime':
        default:
          return new Date(b.createTime) - new Date(a.createTime);
      }
    });

    // 分页
    const total = filteredDishes.length;
    const start = (page - 1) * limit;
    const end = start + limit;
    const pagedDishes = filteredDishes.slice(start, end);
    const hasMore = end < total;

    return mockRequest({
      dishes: pagedDishes,
      total: total,
      page: page,
      limit: limit,
      hasMore: hasMore
    });
  },

  // 获取菜品详情
  getDishDetail(dishId) {
    const dish = dishes.find(d => d._id === dishId);
    if (!dish) {
      return Promise.reject(new Error('菜品不存在'));
    }

    // 增加浏览次数
    dish.viewCount = (dish.viewCount || 0) + 1;

    return mockRequest(dish);
  },

  // 搜索菜品
  searchDishes(keyword, filters = {}) {
    return this.getDishes({ keyword, ...filters });
  },

  // 获取菜品分类
  getCategories() {
    const { DISH_CATEGORIES } = require('./constants');
    return mockRequest(DISH_CATEGORIES);
  },

  // 获取推荐菜品
  getRecommendedDishes(limit = 10) {
    // 基于评分和点餐次数推荐
    const recommendedDishes = [...dishes]
      .sort((a, b) => {
        const scoreA = a.rating * 0.6 + (a.orderCount / 100) * 0.4;
        const scoreB = b.rating * 0.6 + (b.orderCount / 100) * 0.4;
        return scoreB - scoreA;
      })
      .slice(0, limit);

    return mockRequest(recommendedDishes);
  }
};

/**
 * 用户相关API
 */
const userAPI = {
  // 用户登录
  login(userInfo) {
    const userData = {
      _id: generateId(),
      openid: 'mock_openid_' + generateId(),
      nickname: userInfo.nickName || '用户',
      avatar: userInfo.avatarUrl || '',
      familyId: null,
      role: 'member',
      preferences: {
        spicyLevel: 3,
        sweetLevel: 3,
        favoriteCuisine: []
      },
      createTime: new Date().toISOString(),
      lastLoginTime: new Date().toISOString()
    };

    // 保存到本地存储
    wx.setStorageSync('userInfo', userData);

    return mockRequest(userData);
  },

  // 获取用户信息
  getUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      return Promise.reject(new Error('用户未登录'));
    }
    return mockRequest(userInfo);
  },

  // 更新用户信息
  updateUserInfo(userInfo) {
    const currentUserInfo = wx.getStorageSync('userInfo');
    if (!currentUserInfo) {
      return Promise.reject(new Error('用户未登录'));
    }

    const updatedUserInfo = {
      ...currentUserInfo,
      ...userInfo,
      updateTime: new Date().toISOString()
    };

    wx.setStorageSync('userInfo', updatedUserInfo);
    return mockRequest(updatedUserInfo);
  },

  // 更新用户偏好
  updatePreferences(preferences) {
    const currentUserInfo = wx.getStorageSync('userInfo');
    if (!currentUserInfo) {
      return Promise.reject(new Error('用户未登录'));
    }

    const updatedUserInfo = {
      ...currentUserInfo,
      preferences: {
        ...currentUserInfo.preferences,
        ...preferences
      },
      updateTime: new Date().toISOString()
    };

    wx.setStorageSync('userInfo', updatedUserInfo);
    return mockRequest(updatedUserInfo);
  }
};

/**
 * 点餐相关API
 */
const orderAPI = {
  // 创建点餐记录
  createOrder(dishId, note = '') {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      return Promise.reject(new Error('用户未登录'));
    }

    const dish = dishes.find(d => d._id === dishId);
    if (!dish) {
      return Promise.reject(new Error('菜品不存在'));
    }

    const orderData = {
      _id: generateId(),
      userId: userInfo._id,
      dishId: dishId,
      dishName: dish.name,
      dishImage: dish.image,
      cookTime: dish.cookTime,
      difficulty: dish.difficulty,
      note: note,
      status: 'pending',
      orderTime: new Date().toISOString(),
      rating: null,
      comment: ''
    };

    // 保存到本地存储
    const existingOrders = wx.getStorageSync('orders') || [];
    existingOrders.unshift(orderData);
    wx.setStorageSync('orders', existingOrders);

    // 更新菜品点餐次数
    dish.orderCount = (dish.orderCount || 0) + 1;

    return mockRequest(orderData);
  },

  // 获取用户点餐记录
  getUserOrders(page = 1, limit = 20) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      return Promise.reject(new Error('用户未登录'));
    }

    const allOrders = wx.getStorageSync('orders') || [];
    const userOrders = allOrders.filter(order => order.userId === userInfo._id);

    // 分页
    const total = userOrders.length;
    const start = (page - 1) * limit;
    const end = start + limit;
    const pagedOrders = userOrders.slice(start, end);
    const hasMore = end < total;

    return mockRequest({
      orders: pagedOrders,
      total: total,
      page: page,
      limit: limit,
      hasMore: hasMore
    });
  },

  // 更新点餐状态
  updateOrderStatus(orderId, status) {
    const allOrders = wx.getStorageSync('orders') || [];
    const orderIndex = allOrders.findIndex(order => order._id === orderId);

    if (orderIndex === -1) {
      return Promise.reject(new Error('订单不存在'));
    }

    allOrders[orderIndex].status = status;
    allOrders[orderIndex].updateTime = new Date().toISOString();

    wx.setStorageSync('orders', allOrders);
    return mockRequest(allOrders[orderIndex]);
  },

  // 对点餐进行评价
  rateOrder(orderId, rating, comment = '') {
    const allOrders = wx.getStorageSync('orders') || [];
    const orderIndex = allOrders.findIndex(order => order._id === orderId);

    if (orderIndex === -1) {
      return Promise.reject(new Error('订单不存在'));
    }

    allOrders[orderIndex].rating = rating;
    allOrders[orderIndex].comment = comment;
    allOrders[orderIndex].updateTime = new Date().toISOString();

    wx.setStorageSync('orders', allOrders);
    return mockRequest(allOrders[orderIndex]);
  },

  // 获取点餐统计
  getOrderStats() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      return Promise.reject(new Error('用户未登录'));
    }

    const allOrders = wx.getStorageSync('orders') || [];
    const userOrders = allOrders.filter(order => order.userId === userInfo._id);

    const stats = {
      totalOrders: userOrders.length,
      completedOrders: userOrders.filter(order => order.status === 'completed').length,
      pendingOrders: userOrders.filter(order => order.status === 'pending').length,
      cookingOrders: userOrders.filter(order => order.status === 'cooking').length
    };

    return mockRequest(stats);
  }
};

/**
 * 收藏相关API
 */
const favoriteAPI = {
  // 添加收藏
  addToFavorites(dishId) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      return Promise.reject(new Error('用户未登录'));
    }

    const dish = dishes.find(d => d._id === dishId);
    if (!dish) {
      return Promise.reject(new Error('菜品不存在'));
    }

    const favoriteData = {
      _id: generateId(),
      userId: userInfo._id,
      dishId: dishId,
      createTime: new Date().toISOString()
    };

    // 保存到本地存储
    const existingFavorites = wx.getStorageSync('favorites') || [];

    // 检查是否已收藏
    const isAlreadyFavorited = existingFavorites.some(fav =>
      fav.userId === userInfo._id && fav.dishId === dishId
    );

    if (isAlreadyFavorited) {
      return Promise.reject(new Error('已经收藏过了'));
    }

    existingFavorites.push(favoriteData);
    wx.setStorageSync('favorites', existingFavorites);

    return mockRequest(favoriteData);
  },

  // 取消收藏
  removeFromFavorites(dishId) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      return Promise.reject(new Error('用户未登录'));
    }

    const existingFavorites = wx.getStorageSync('favorites') || [];
    const filteredFavorites = existingFavorites.filter(fav =>
      !(fav.userId === userInfo._id && fav.dishId === dishId)
    );

    wx.setStorageSync('favorites', filteredFavorites);
    return mockRequest({ success: true });
  },

  // 获取收藏列表
  getFavorites(page = 1, limit = 20) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      return Promise.reject(new Error('用户未登录'));
    }

    const allFavorites = wx.getStorageSync('favorites') || [];
    const userFavorites = allFavorites.filter(fav => fav.userId === userInfo._id);

    // 获取收藏的菜品详情
    const favoriteDishes = userFavorites.map(fav => {
      const dish = dishes.find(d => d._id === fav.dishId);
      return {
        ...fav,
        dish: dish
      };
    }).filter(fav => fav.dish); // 过滤掉不存在的菜品

    // 分页
    const total = favoriteDishes.length;
    const start = (page - 1) * limit;
    const end = start + limit;
    const pagedFavorites = favoriteDishes.slice(start, end);
    const hasMore = end < total;

    return mockRequest({
      favorites: pagedFavorites,
      total: total,
      page: page,
      limit: limit,
      hasMore: hasMore
    });
  },

  // 检查是否已收藏
  checkFavorite(dishId) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      return mockRequest({ isFavorited: false });
    }

    const allFavorites = wx.getStorageSync('favorites') || [];
    const isFavorited = allFavorites.some(fav =>
      fav.userId === userInfo._id && fav.dishId === dishId
    );

    return mockRequest({ isFavorited });
  }
};

/**
 * 统计相关API
 */
const statsAPI = {
  // 获取热门菜品
  getPopularDishes(limit = 10) {
    return dishAPI.getDishes({
      sortBy: 'orderCount',
      limit: limit
    });
  },

  // 获取用户统计
  getUserStats() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      return Promise.reject(new Error('用户未登录'));
    }

    const allOrders = wx.getStorageSync('orders') || [];
    const allFavorites = wx.getStorageSync('favorites') || [];

    const userOrders = allOrders.filter(order => order.userId === userInfo._id);
    const userFavorites = allFavorites.filter(fav => fav.userId === userInfo._id);

    const stats = {
      totalOrders: userOrders.length,
      totalFavorites: userFavorites.length,
      completedOrders: userOrders.filter(order => order.status === 'completed').length,
      averageRating: userOrders.filter(order => order.rating).reduce((sum, order) => sum + order.rating, 0) / userOrders.filter(order => order.rating).length || 0
    };

    return mockRequest(stats);
  },

  // 记录用户行为
  recordUserAction(action, data = {}) {
    // 本地版本可以简单记录到控制台或本地存储
    console.log('用户行为记录:', action, data);
    return mockRequest({ success: true });
  }
};

/**
 * 文件上传（本地版本 - 模拟）
 * @param {string} filePath 本地文件路径
 * @param {string} cloudPath 云存储路径
 * @returns {Promise} 返回Promise对象
 */
function uploadFile(filePath, cloudPath) {
  // 本地版本直接返回文件路径
  return mockRequest(filePath);
}

/**
 * 文件下载（本地版本 - 模拟）
 * @param {string} fileID 文件ID
 * @returns {Promise} 返回Promise对象
 */
function downloadFile(fileID) {
  // 本地版本直接返回文件ID
  return mockRequest(fileID);
}

/**
 * 批量请求处理
 * @param {Array} requests 请求数组
 * @returns {Promise} 返回Promise对象
 */
function batchRequest(requests) {
  return Promise.all(requests.map(request => {
    if (typeof request === 'function') {
      return request();
    }
    return request;
  }));
}

/**
 * 请求重试机制
 * @param {Function} requestFn 请求函数
 * @param {number} maxRetries 最大重试次数
 * @param {number} delay 重试延迟时间
 * @returns {Promise} 返回Promise对象
 */
function retryRequest(requestFn, maxRetries = 3, delay = 1000) {
  return new Promise((resolve, reject) => {
    let retries = 0;
    
    function attempt() {
      requestFn()
        .then(resolve)
        .catch(error => {
          retries++;
          if (retries <= maxRetries) {
            setTimeout(attempt, delay * retries);
          } else {
            reject(error);
          }
        });
    }
    
    attempt();
  });
}

module.exports = {
  mockRequest,
  dishAPI,
  userAPI,
  orderAPI,
  favoriteAPI,
  statsAPI,
  uploadFile,
  downloadFile,
  batchRequest,
  retryRequest
};
