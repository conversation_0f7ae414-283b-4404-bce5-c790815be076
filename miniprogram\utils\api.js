// utils/api.js - API请求工具类

/**
 * 云函数调用封装
 * @param {string} name 云函数名称
 * @param {object} data 请求参数
 * @returns {Promise} 返回Promise对象
 */
function callCloudFunction(name, data = {}) {
  return new Promise((resolve, reject) => {
    wx.cloud.callFunction({
      name: name,
      data: data,
      success: (res) => {
        if (res.result && res.result.code === 0) {
          resolve(res.result.data);
        } else {
          const error = new Error(res.result?.message || '请求失败');
          error.code = res.result?.code || -1;
          reject(error);
        }
      },
      fail: (error) => {
        console.error(`云函数 ${name} 调用失败:`, error);
        reject(new Error('网络请求失败，请检查网络连接'));
      }
    });
  });
}

/**
 * 菜品相关API
 */
const dishAPI = {
  // 获取菜品列表
  getDishes(params = {}) {
    return callCloudFunction('getDishes', params);
  },

  // 获取菜品详情
  getDishDetail(dishId) {
    return callCloudFunction('getDishDetail', { dishId });
  },

  // 搜索菜品
  searchDishes(keyword, filters = {}) {
    return callCloudFunction('searchDishes', { keyword, ...filters });
  },

  // 获取菜品分类
  getCategories() {
    return callCloudFunction('getCategories');
  },

  // 获取推荐菜品
  getRecommendedDishes(limit = 10) {
    return callCloudFunction('getRecommendedDishes', { limit });
  }
};

/**
 * 用户相关API
 */
const userAPI = {
  // 用户登录
  login(userInfo) {
    return callCloudFunction('login', { userInfo });
  },

  // 获取用户信息
  getUserInfo() {
    return callCloudFunction('getUserInfo');
  },

  // 更新用户信息
  updateUserInfo(userInfo) {
    return callCloudFunction('updateUserInfo', userInfo);
  },

  // 更新用户偏好
  updatePreferences(preferences) {
    return callCloudFunction('updatePreferences', preferences);
  }
};

/**
 * 点餐相关API
 */
const orderAPI = {
  // 创建点餐记录
  createOrder(dishId, note = '') {
    return callCloudFunction('createOrder', { dishId, note });
  },

  // 获取用户点餐记录
  getUserOrders(page = 1, limit = 20) {
    return callCloudFunction('getUserOrders', { page, limit });
  },

  // 更新点餐状态
  updateOrderStatus(orderId, status) {
    return callCloudFunction('updateOrderStatus', { orderId, status });
  },

  // 对点餐进行评价
  rateOrder(orderId, rating, comment = '') {
    return callCloudFunction('rateOrder', { orderId, rating, comment });
  },

  // 获取点餐统计
  getOrderStats() {
    return callCloudFunction('getOrderStats');
  }
};

/**
 * 收藏相关API
 */
const favoriteAPI = {
  // 添加收藏
  addToFavorites(dishId) {
    return callCloudFunction('addToFavorites', { dishId });
  },

  // 取消收藏
  removeFromFavorites(dishId) {
    return callCloudFunction('removeFromFavorites', { dishId });
  },

  // 获取收藏列表
  getFavorites(page = 1, limit = 20) {
    return callCloudFunction('getFavorites', { page, limit });
  },

  // 检查是否已收藏
  checkFavorite(dishId) {
    return callCloudFunction('checkFavorite', { dishId });
  }
};

/**
 * 统计相关API
 */
const statsAPI = {
  // 获取热门菜品（使用getDishes云函数，按orderCount排序）
  getPopularDishes(limit = 10) {
    return callCloudFunction('getDishes', {
      sortBy: 'orderCount',
      limit: limit
    });
  },

  // 获取用户统计
  getUserStats() {
    return callCloudFunction('getUserStats');
  },

  // 记录用户行为
  recordUserAction(action, data = {}) {
    return callCloudFunction('recordUserAction', { action, data });
  }
};

/**
 * 文件上传
 * @param {string} filePath 本地文件路径
 * @param {string} cloudPath 云存储路径
 * @returns {Promise} 返回Promise对象
 */
function uploadFile(filePath, cloudPath) {
  return new Promise((resolve, reject) => {
    wx.cloud.uploadFile({
      cloudPath: cloudPath,
      filePath: filePath,
      success: (res) => {
        resolve(res.fileID);
      },
      fail: (error) => {
        console.error('文件上传失败:', error);
        reject(new Error('文件上传失败'));
      }
    });
  });
}

/**
 * 文件下载
 * @param {string} fileID 云存储文件ID
 * @returns {Promise} 返回Promise对象
 */
function downloadFile(fileID) {
  return new Promise((resolve, reject) => {
    wx.cloud.downloadFile({
      fileID: fileID,
      success: (res) => {
        resolve(res.tempFilePath);
      },
      fail: (error) => {
        console.error('文件下载失败:', error);
        reject(new Error('文件下载失败'));
      }
    });
  });
}

/**
 * 批量请求处理
 * @param {Array} requests 请求数组
 * @returns {Promise} 返回Promise对象
 */
function batchRequest(requests) {
  return Promise.all(requests.map(request => {
    if (typeof request === 'function') {
      return request();
    }
    return request;
  }));
}

/**
 * 请求重试机制
 * @param {Function} requestFn 请求函数
 * @param {number} maxRetries 最大重试次数
 * @param {number} delay 重试延迟时间
 * @returns {Promise} 返回Promise对象
 */
function retryRequest(requestFn, maxRetries = 3, delay = 1000) {
  return new Promise((resolve, reject) => {
    let retries = 0;
    
    function attempt() {
      requestFn()
        .then(resolve)
        .catch(error => {
          retries++;
          if (retries <= maxRetries) {
            setTimeout(attempt, delay * retries);
          } else {
            reject(error);
          }
        });
    }
    
    attempt();
  });
}

module.exports = {
  callCloudFunction,
  dishAPI,
  userAPI,
  orderAPI,
  favoriteAPI,
  statsAPI,
  uploadFile,
  downloadFile,
  batchRequest,
  retryRequest
};
