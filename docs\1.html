<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>今天吃什么 - 解决你的选择困难症</title>
    <script src="https://res.gemcoder.com/js/reload.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <script>
      tailwind.config = {
                  theme: {
                      extend: {
                          colors: {
                              primary: '#FF6B35',
                              secondary: '#FFD166',
                              accent: '#06D6A0',
                              dark: '#118AB2',
                              light: '#F7F7F9',
                          },
                          fontFamily: {
                              sans: ['Inter', 'system-ui', 'sans-serif'],
                          },
                          spacing: {
                              '128': '32rem',
                          }
                      },
                  }
              }
    </script>
    <style type="text/tailwindcss">
      @layer utilities {
          .content-auto {
              content-visibility: auto;
          }
          .text-shadow {
              text-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .animate-spin-slow {
              animation: spin 3s linear infinite;
          }
          .animate-bounce-slow {
              animation: bounce 2s infinite;
          }
          .bg-gradient-food {
              background: linear-gradient(135deg, #FF6B35 0%, #FFD166 100%);
          }
          .card-hover {
              transition: all 0.3s ease;
          }
          .card-hover:hover {
              transform: translateY(-5px);
              box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
          }
      }
    </style>
  </head>
  <body class="bg-light min-h-screen font-sans text-gray-800">
    <!-- 顶部导航栏 -->
    <header
      class="fixed top-0 left-0 right-0 bg-white/90 backdrop-blur-sm z-50 shadow-sm transition-all duration-300"
      id="navbar"
    >
      <div
        class="container mx-auto px-4 py-3 flex justify-between items-center"
      >
        <div class="flex items-center space-x-2">
          <i class="fas fa-utensils text-primary text-2xl"> </i>
          <h1 class="text-xl font-bold text-gray-800">今天吃什么</h1>
        </div>
        <div class="flex items-center space-x-4">
          <button
            class="text-gray-600 hover:text-primary transition-colors"
            id="almanacBtn"
          >
            <i class="fas fa-calendar-alt text-lg"> </i>
          </button>
          <button
            class="text-gray-600 hover:text-primary transition-colors"
            id="settingsBtn"
          >
            <i class="fas fa-cog text-lg"> </i>
          </button>
          <button
            class="text-gray-600 hover:text-primary transition-colors"
            id="historyBtn"
          >
            <i class="fas fa-history text-lg"> </i>
          </button>
        </div>
      </div>
    </header>
    <!-- 主内容区 -->
    <main class="container mx-auto px-4 pt-24 pb-20">
      <!-- 页面容器 -->
      <div class="page-container">
        <!-- 首页内容 -->
        <div class="page active" id="homePage">
          <!-- 天气和推荐区域 -->
          <section class="mb-8">
            <div
              class="bg-white rounded-2xl shadow-md overflow-hidden card-hover"
            >
              <div class="p-6">
                <div class="flex justify-between items-start mb-4">
                  <div>
                    <h2 class="text-lg font-semibold text-gray-700 mb-1">
                      今日推荐
                    </h2>
                    <p class="text-sm text-gray-500">根据天气和你的口味</p>
                  </div>
                  <div
                    class="flex items-center space-x-1 bg-blue-50 px-3 py-1 rounded-full"
                  >
                    <i class="fas fa-sun text-yellow-500"> </i>
                    <span class="text-sm font-medium"> 25°C </span>
                  </div>
                </div>
                <div class="flex items-center space-x-4">
                  <div
                    class="w-16 h-16 rounded-full overflow-hidden flex-shrink-0"
                  >
                    <img
                      alt="火锅"
                      class="w-full h-full object-cover"
                      src="https://design.gemcoder.com/staticResource/echoAiSystemImages/45582fe53528a55d69be00424601c14c.png"
                    />
                  </div>
                  <div class="flex-grow">
                    <h3 class="font-bold text-lg text-gray-800">火锅</h3>
                    <p class="text-sm text-gray-500">
                      天气转凉，来顿热腾腾的火锅吧！
                    </p>
                  </div>
                  <button
                    class="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-medium hover:bg-primary/20 transition-colors"
                  >
                    去看看
                  </button>
                </div>
              </div>
            </div>
          </section>
          <!-- 主要功能区 - 随机选择 -->
          <section class="mb-10">
            <div
              class="bg-gradient-food rounded-3xl shadow-lg overflow-hidden relative"
            >
              <div class="absolute inset-0 bg-black/5"></div>
              <div class="p-8 relative z-10">
                <h2
                  class="text-[clamp(1.5rem,5vw,2.5rem)] font-bold text-white text-center mb-6 text-shadow"
                >
                  不知道吃什么？
                </h2>
                <p class="text-white/90 text-center mb-8 max-w-md mx-auto">
                  让我们帮你做决定，轻轻一点，解决选择困难症
                </p>
                <div
                  class="relative mx-auto max-w-md aspect-square max-h-[300px] mb-8"
                >
                  <div
                    class="absolute inset-0 rounded-full border-4 border-white/30 animate-spin-slow"
                  ></div>
                  <div
                    class="absolute inset-4 rounded-full bg-white flex items-center justify-center shadow-lg"
                  >
                    <div class="text-center p-4">
                      <div
                        class="text-2xl font-bold text-primary mb-2 h-10"
                        id="foodResult"
                      >
                        点击开始
                      </div>
                      <p class="text-gray-500 text-sm" id="foodCategory">
                        美食分类
                      </p>
                    </div>
                  </div>
                </div>
                <button
                  class="w-full py-4 bg-white text-primary rounded-full font-bold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all active:translate-y-0 focus:outline-none focus:ring-2 focus:ring-white/50"
                  id="randomBtn"
                >
                  <i class="fas fa-random mr-2"> </i>
                  随机选择
                </button>
              </div>
            </div>
          </section>
          <!-- 分类选择区 -->
          <section class="mb-10">
            <h2 class="text-xl font-bold text-gray-800 mb-4">按分类选择</h2>
            <div class="grid grid-cols-4 gap-4">
              <button
                class="category-btn flex flex-col items-center p-3 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow"
              >
                <div
                  class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-2"
                >
                  <i class="fas fa-utensils text-red-500"> </i>
                </div>
                <span class="text-xs font-medium text-gray-700"> 中餐 </span>
              </button>
              <button
                class="category-btn flex flex-col items-center p-3 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow"
              >
                <div
                  class="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center mb-2"
                >
                  <i class="fas fa-utensils text-yellow-500"> </i>
                </div>
                <span class="text-xs font-medium text-gray-700"> 西餐 </span>
              </button>
              <button
                class="category-btn flex flex-col items-center p-3 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow"
              >
                <div
                  class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-2"
                >
                  <i class="fas fa-utensils text-green-500"> </i>
                </div>
                <span class="text-xs font-medium text-gray-700"> 日料 </span>
              </button>
              <button
                class="category-btn flex flex-col items-center p-3 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow"
              >
                <div
                  class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-2"
                >
                  <i class="fas fa-utensils text-purple-500"> </i>
                </div>
                <span class="text-xs font-medium text-gray-700"> 韩餐 </span>
              </button>
              <button
                class="category-btn flex flex-col items-center p-3 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow"
              >
                <div
                  class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-2"
                >
                  <i class="fas fa-ice-cream text-blue-500"> </i>
                </div>
                <span class="text-xs font-medium text-gray-700"> 甜点 </span>
              </button>
              <button
                class="category-btn flex flex-col items-center p-3 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow"
              >
                <div
                  class="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mb-2"
                >
                  <i class="fas fa-coffee text-orange-500"> </i>
                </div>
                <span class="text-xs font-medium text-gray-700"> 饮品 </span>
              </button>
              <button
                class="category-btn flex flex-col items-center p-3 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow"
              >
                <div
                  class="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center mb-2"
                >
                  <i class="fas fa-pepper-hot text-pink-500"> </i>
                </div>
                <span class="text-xs font-medium text-gray-700"> 小吃 </span>
              </button>
              <button
                class="category-btn flex flex-col items-center p-3 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow"
              >
                <div
                  class="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-2"
                >
                  <i class="fas fa-ellipsis-h text-gray-500"> </i>
                </div>
                <span class="text-xs font-medium text-gray-700"> 更多 </span>
              </button>
            </div>
          </section>
          <!-- 自定义菜单区 -->
          <section>
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-xl font-bold text-gray-800">我的菜单</h2>
              <button
                class="text-primary text-sm font-medium flex items-center"
                id="addMenuBtn"
              >
                <i class="fas fa-plus-circle mr-1"> </i>
                添加
              </button>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div
                class="bg-white rounded-xl shadow-sm overflow-hidden card-hover"
              >
                <div class="h-32 overflow-hidden">
                  <img
                    alt="工作日午餐"
                    class="w-full h-full object-cover"
                    src="https://design.gemcoder.com/staticResource/echoAiSystemImages/93592c9831930b41bc8538234b8f94fb.png"
                  />
                </div>
                <div class="p-4">
                  <h3 class="font-bold text-gray-800 mb-1">工作日午餐</h3>
                  <p class="text-xs text-gray-500 mb-3">12 个选项</p>
                  <button
                    class="w-full py-2 bg-primary/10 text-primary rounded-lg text-sm font-medium hover:bg-primary/20 transition-colors"
                  >
                    随机选择
                  </button>
                </div>
              </div>
              <div
                class="bg-white rounded-xl shadow-sm overflow-hidden card-hover"
              >
                <div class="h-32 overflow-hidden">
                  <img
                    alt="周末晚餐"
                    class="w-full h-full object-cover"
                    src="https://design.gemcoder.com/staticResource/echoAiSystemImages/aa9d29ac776b4d666e1d132135aeaa08.png"
                  />
                </div>
                <div class="p-4">
                  <h3 class="font-bold text-gray-800 mb-1">周末晚餐</h3>
                  <p class="text-xs text-gray-500 mb-3">8 个选项</p>
                  <button
                    class="w-full py-2 bg-primary/10 text-primary rounded-lg text-sm font-medium hover:bg-primary/20 transition-colors"
                  >
                    随机选择
                  </button>
                </div>
              </div>
            </div>
          </section>
        </div>
        <!-- 发现页面 -->
        <div class="page hidden" id="discoverPage">
          <section class="mb-8">
            <div
              class="bg-white rounded-2xl shadow-md overflow-hidden card-hover"
            >
              <div class="p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">美食趋势</h2>
                <div class="h-64">
                  <canvas id="foodTrendChart"> </canvas>
                </div>
              </div>
            </div>
          </section>
          <section class="mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4">热门推荐</h2>
            <div class="grid grid-cols-2 gap-4">
              <div
                class="bg-white rounded-xl shadow-sm overflow-hidden card-hover"
              >
                <div class="h-32 overflow-hidden">
                  <img
                    alt="网红奶茶"
                    class="w-full h-full object-cover"
                    src="https://design.gemcoder.com/staticResource/echoAiSystemImages/262e2da230ab7fd36c7b9150af02ce47.png"
                  />
                </div>
                <div class="p-4">
                  <h3 class="font-bold text-gray-800">网红奶茶</h3>
                  <p class="text-xs text-gray-500 mt-1">最近流行的新口味</p>
                </div>
              </div>
              <div
                class="bg-white rounded-xl shadow-sm overflow-hidden card-hover"
              >
                <div class="h-32 overflow-hidden">
                  <img
                    alt="轻食沙拉"
                    class="w-full h-full object-cover"
                    src="https://design.gemcoder.com/staticResource/echoAiSystemImages/c58e56a8dc8a7636eaa49ed8d5ae280c.png"
                  />
                </div>
                <div class="p-4">
                  <h3 class="font-bold text-gray-800">轻食沙拉</h3>
                  <p class="text-xs text-gray-500 mt-1">健康低脂新选择</p>
                </div>
              </div>
            </div>
          </section>
          <section>
            <h2 class="text-xl font-bold text-gray-800 mb-4">附近美食</h2>
            <div class="space-y-4">
              <div class="bg-white rounded-xl shadow-sm p-4 card-hover">
                <div class="flex items-center space-x-4">
                  <div class="w-16 h-16 rounded-lg overflow-hidden">
                    <img
                      alt="寿司店"
                      class="w-full h-full object-cover"
                      src="https://design.gemcoder.com/staticResource/echoAiSystemImages/9934f6723b37b18bb43eae02837065a9.png"
                    />
                  </div>
                  <div class="flex-grow">
                    <h3 class="font-bold text-gray-800">渔寿司</h3>
                    <div class="flex items-center text-xs text-gray-500 mt-1">
                      <i class="fas fa-star text-yellow-400 mr-1"> </i>
                      <span> 4.8 </span>
                      <span class="mx-2"> • </span>
                      <span> 日料 </span>
                      <span class="mx-2"> • </span>
                      <span> 1.2km </span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="bg-white rounded-xl shadow-sm p-4 card-hover">
                <div class="flex items-center space-x-4">
                  <div class="w-16 h-16 rounded-lg overflow-hidden">
                    <img
                      alt="火锅店"
                      class="w-full h-full object-cover"
                      src="https://design.gemcoder.com/staticResource/echoAiSystemImages/0b835e0bc008056fd63812969dac14e1.png"
                    />
                  </div>
                  <div class="flex-grow">
                    <h3 class="font-bold text-gray-800">老四川火锅</h3>
                    <div class="flex items-center text-xs text-gray-500 mt-1">
                      <i class="fas fa-star text-yellow-400 mr-1"> </i>
                      <span> 4.6 </span>
                      <span class="mx-2"> • </span>
                      <span> 火锅 </span>
                      <span class="mx-2"> • </span>
                      <span> 2.5km </span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="bg-white rounded-xl shadow-sm p-4 card-hover">
                <div class="flex items-center space-x-4">
                  <div class="w-16 h-16 rounded-lg overflow-hidden">
                    <img
                      alt="咖啡馆"
                      class="w-full h-full object-cover"
                      src="https://design.gemcoder.com/staticResource/echoAiSystemImages/e143171dc62420f84ba748a5b8331280.png"
                    />
                  </div>
                  <div class="flex-grow">
                    <h3 class="font-bold text-gray-800">转角咖啡</h3>
                    <div class="flex items-center text-xs text-gray-500 mt-1">
                      <i class="fas fa-star text-yellow-400 mr-1"> </i>
                      <span> 4.7 </span>
                      <span class="mx-2"> • </span>
                      <span> 咖啡甜点 </span>
                      <span class="mx-2"> • </span>
                      <span> 800m </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
        <!-- 收藏页面 -->
        <div class="page hidden" id="favoritePage">
          <div class="text-center py-10">
            <div
              class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <i class="fas fa-bookmark text-gray-300 text-4xl"> </i>
            </div>
            <h3 class="text-xl font-bold text-gray-700 mb-2">暂无收藏</h3>
            <p class="text-gray-500 max-w-xs mx-auto">
              当你发现喜欢的美食或餐厅时，可以点击收藏按钮保存起来
            </p>
            <button
              class="mt-6 bg-primary text-white px-6 py-2 rounded-full font-medium hover:bg-primary/90 transition-colors"
              id="goDiscoverBtn"
            >
              去发现美食
            </button>
          </div>
        </div>
        <!-- 我的页面 -->
        <div class="page hidden" id="profilePage">
          <section class="mb-8">
            <div
              class="bg-gradient-food rounded-2xl shadow-md overflow-hidden relative"
            >
              <div class="absolute inset-0 bg-black/20"></div>
              <div class="p-6 relative z-10">
                <div class="flex items-center space-x-4">
                  <div
                    class="w-16 h-16 rounded-full overflow-hidden border-4 border-white"
                  >
                    <img
                      alt="用户头像"
                      class="w-full h-full object-cover"
                      src="https://design.gemcoder.com/staticResource/echoAiSystemImages/fcd837c86cafd972cb61c005269446d4.png"
                    />
                  </div>
                  <div class="text-white">
                    <h3 class="text-xl font-bold">美食爱好者</h3>
                    <p class="text-white/80 text-sm">今天也要好好吃饭</p>
                  </div>
                </div>
              </div>
            </div>
          </section>
          <section class="mb-8">
            <div class="grid grid-cols-3 gap-4">
              <div
                class="bg-white rounded-xl shadow-sm p-4 text-center card-hover"
              >
                <p class="text-2xl font-bold text-primary" id="favoriteCount">
                  0
                </p>
                <p class="text-sm text-gray-500">收藏</p>
              </div>
              <div
                class="bg-white rounded-xl shadow-sm p-4 text-center card-hover"
              >
                <p class="text-2xl font-bold text-primary" id="historyCount">
                  0
                </p>
                <p class="text-sm text-gray-500">历史</p>
              </div>
              <div
                class="bg-white rounded-xl shadow-sm p-4 text-center card-hover"
              >
                <p class="text-2xl font-bold text-primary" id="menuCount">2</p>
                <p class="text-sm text-gray-500">我的菜单</p>
              </div>
            </div>
          </section>
          <section>
            <h2 class="text-xl font-bold text-gray-800 mb-4">设置</h2>
            <div class="space-y-2">
              <button
                class="w-full bg-white rounded-xl shadow-sm p-4 text-left flex justify-between items-center card-hover"
                id="profileSettingsBtn"
              >
                <div class="flex items-center space-x-3">
                  <div
                    class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center"
                  >
                    <i class="fas fa-user text-blue-500"> </i>
                  </div>
                  <span> 个人信息 </span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"> </i>
              </button>
              <button
                class="w-full bg-white rounded-xl shadow-sm p-4 text-left flex justify-between items-center card-hover"
                id="notificationSettingsBtn"
              >
                <div class="flex items-center space-x-3">
                  <div
                    class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center"
                  >
                    <i class="fas fa-bell text-green-500"> </i>
                  </div>
                  <span> 通知设置 </span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"> </i>
              </button>
              <button
                class="w-full bg-white rounded-xl shadow-sm p-4 text-left flex justify-between items-center card-hover"
                id="aboutBtn"
              >
                <div class="flex items-center space-x-3">
                  <div
                    class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center"
                  >
                    <i class="fas fa-info-circle text-purple-500"> </i>
                  </div>
                  <span> 关于我们 </span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"> </i>
              </button>
            </div>
          </section>
        </div>
      </div>
      <nav class="fixed bottom-0 left-0 right-0 bg-white shadow-lg z-40">
        <div class="container mx-auto">
          <div class="flex justify-around">
            <a
              class="flex flex-col items-center py-3 px-4 text-primary"
              href="javascript:void(0);"
            >
              <i class="fas fa-home text-xl mb-1"> </i>
              <span class="text-xs font-medium"> 首页 </span>
            </a>
            <a
              class="flex flex-col items-center py-3 px-4 text-gray-400 hover:text-primary transition-colors"
              href="javascript:void(0);"
            >
              <i class="fas fa-list text-xl mb-1"> </i>
              <span class="text-xs font-medium"> 发现 </span>
            </a>
            <a
              class="flex flex-col items-center py-3 px-4 text-gray-400 hover:text-primary transition-colors"
              href="javascript:void(0);"
            >
              <i class="fas fa-bookmark text-xl mb-1"> </i>
              <span class="text-xs font-medium"> 收藏 </span>
            </a>
            <a
              class="flex flex-col items-center py-3 px-4 text-gray-400 hover:text-primary transition-colors"
              href="javascript:void(0);"
            >
              <i class="fas fa-user text-xl mb-1"> </i>
              <span class="text-xs font-medium"> 我的 </span>
            </a>
          </div>
        </div>
      </nav>
      <!-- 历史记录弹窗 -->
      <div
        class="fixed inset-0 bg-black/50 z-50 hidden flex items-center justify-center p-4"
        id="historyModal"
      >
        <div
          class="bg-white rounded-2xl w-full max-w-md max-h-[80vh] overflow-hidden flex flex-col"
        >
          <div class="p-5 border-b flex justify-between items-center">
            <h3 class="text-xl font-bold text-gray-800">历史记录</h3>
            <button
              class="text-gray-500 hover:text-gray-700"
              id="closeHistoryBtn"
            >
              <i class="fas fa-times"> </i>
            </button>
          </div>
          <div class="flex-grow overflow-y-auto p-5">
            <div class="space-y-4">
              <div class="flex items-center justify-between pb-3 border-b">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 rounded-full overflow-hidden">
                    <img
                      alt="汉堡"
                      class="w-full h-full object-cover"
                      src="https://design.gemcoder.com/staticResource/echoAiSystemImages/f44193403999d09564badfa671f97bfc.png"
                    />
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800">汉堡</h4>
                    <p class="text-xs text-gray-500">今天 12:30</p>
                  </div>
                </div>
                <button class="text-gray-400 hover:text-primary">
                  <i class="fas fa-redo"> </i>
                </button>
              </div>
              <div class="flex items-center justify-between pb-3 border-b">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 rounded-full overflow-hidden">
                    <img
                      alt="寿司"
                      class="w-full h-full object-cover"
                      src="https://design.gemcoder.com/staticResource/echoAiSystemImages/78aa282bf0e1ba4555db63449aa5f1de.png"
                    />
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800">寿司</h4>
                    <p class="text-xs text-gray-500">昨天 19:45</p>
                  </div>
                </div>
                <button class="text-gray-400 hover:text-primary">
                  <i class="fas fa-redo"> </i>
                </button>
              </div>
              <div class="flex items-center justify-between pb-3 border-b">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 rounded-full overflow-hidden">
                    <img
                      alt="牛肉面"
                      class="w-full h-full object-cover"
                      src="https://design.gemcoder.com/staticResource/echoAiSystemImages/b206e8f1880eed9b2bfb509af064aee8.png"
                    />
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800">牛肉面</h4>
                    <p class="text-xs text-gray-500">昨天 12:15</p>
                  </div>
                </div>
                <button class="text-gray-400 hover:text-primary">
                  <i class="fas fa-redo"> </i>
                </button>
              </div>
              <div class="flex items-center justify-between pb-3 border-b">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 rounded-full overflow-hidden">
                    <img
                      alt="披萨"
                      class="w-full h-full object-cover"
                      src="https://design.gemcoder.com/staticResource/echoAiSystemImages/e8a80f7861e0d445fd239d239e142e9e.png"
                    />
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800">披萨</h4>
                    <p class="text-xs text-gray-500">前天 20:00</p>
                  </div>
                </div>
                <button class="text-gray-400 hover:text-primary">
                  <i class="fas fa-redo"> </i>
                </button>
              </div>
            </div>
          </div>
          <div class="p-5 border-t">
            <button
              class="w-full py-2 text-gray-500 hover:text-gray-700 text-sm font-medium"
            >
              清除全部历史
            </button>
          </div>
        </div>
      </div>
      <!-- 设置弹窗 -->
      <div
        class="fixed inset-0 bg-black/50 z-50 hidden flex items-center justify-center p-4"
        id="settingsModal"
      >
        <div
          class="bg-white rounded-2xl w-full max-w-md max-h-[80vh] overflow-hidden flex flex-col"
        >
          <div class="p-5 border-b flex justify-between items-center">
            <h3 class="text-xl font-bold text-gray-800">设置</h3>
            <button
              class="text-gray-500 hover:text-gray-700"
              id="closeSettingsBtn"
            >
              <i class="fas fa-times"> </i>
            </button>
          </div>
          <div class="flex-grow overflow-y-auto p-5">
            <div class="space-y-6">
              <div>
                <h4 class="text-sm font-medium text-gray-500 mb-3">偏好设置</h4>
                <div class="space-y-3">
                  <label
                    class="flex items-center justify-between p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                  >
                    <div class="flex items-center space-x-3">
                      <i class="fas fa-utensils text-primary"> </i>
                      <span> 显示推荐 </span>
                    </div>
                    <div class="relative w-10 h-5 bg-gray-200 rounded-full">
                      <div
                        class="absolute left-0 top-0 w-5 h-5 bg-white rounded-full shadow transform translate-x-5 transition-transform bg-primary"
                      ></div>
                    </div>
                  </label>
                  <label
                    class="flex items-center justify-between p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                  >
                    <div class="flex items-center space-x-3">
                      <i class="fas fa-sun text-yellow-500"> </i>
                      <span> 根据天气推荐 </span>
                    </div>
                    <div class="relative w-10 h-5 bg-gray-200 rounded-full">
                      <div
                        class="absolute left-0 top-0 w-5 h-5 bg-white rounded-full shadow transform translate-x-5 transition-transform bg-primary"
                      ></div>
                    </div>
                  </label>
                  <label
                    class="flex items-center justify-between p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                  >
                    <div class="flex items-center space-x-3">
                      <i class="fas fa-clock text-blue-500"> </i>
                      <span> 根据时间推荐 </span>
                    </div>
                    <div class="relative w-10 h-5 bg-gray-200 rounded-full">
                      <div
                        class="absolute left-0 top-0 w-5 h-5 bg-white rounded-full shadow transform translate-x-5 transition-transform bg-primary"
                      ></div>
                    </div>
                  </label>
                </div>
              </div>
              <div>
                <h4 class="text-sm font-medium text-gray-500 mb-3">饮食禁忌</h4>
                <div class="flex flex-wrap gap-2">
                  <span
                    class="px-3 py-1 bg-red-100 text-red-600 rounded-full text-sm flex items-center"
                  >
                    辣
                    <button class="ml-1">
                      <i class="fas fa-times-circle"> </i>
                    </button>
                  </span>
                  <span
                    class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm flex items-center"
                  >
                    海鲜
                    <button class="ml-1">
                      <i class="fas fa-times-circle"> </i>
                    </button>
                  </span>
                  <button
                    class="px-3 py-1 bg-gray-100 text-gray-500 rounded-full text-sm hover:bg-gray-200 transition-colors"
                  >
                    <i class="fas fa-plus mr-1"> </i>
                    添加
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 黄历弹窗 -->
      <div
        class="fixed inset-0 bg-black/50 z-50 hidden flex items-center justify-center p-4"
        id="almanacModal"
      >
        <div
          class="bg-white rounded-2xl w-full max-w-md max-h-[80vh] overflow-hidden flex flex-col"
        >
          <div class="p-5 border-b flex justify-between items-center">
            <h3 class="text-xl font-bold text-gray-800">今日黄历</h3>
            <button
              class="text-gray-500 hover:text-gray-700"
              id="closeAlmanacBtn"
            >
              <i class="fas fa-times"> </i>
            </button>
          </div>
          <div class="flex-grow overflow-y-auto p-5">
            <div class="text-center mb-6">
              <h4 class="text-2xl font-bold text-gray-800" id="almanacDate">
                2023年11月7日 星期二
              </h4>
              <p class="text-sm text-gray-500 mt-1" id="almanacLunar">
                癸卯年 九月廿四
              </p>
              <div
                class="mt-4 inline-block px-4 py-2 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium"
                id="almanacSolarTerm"
              >
                立冬
              </div>
            </div>
            <div class="grid grid-cols-2 gap-4 mb-6">
              <div class="bg-gray-50 p-4 rounded-xl text-center">
                <p class="text-sm text-gray-500 mb-1">今日宜食</p>
                <p class="font-bold text-primary text-lg" id="almanacFood">
                  温补食材、根茎类
                </p>
              </div>
              <div class="bg-gray-50 p-4 rounded-xl text-center">
                <p class="text-sm text-gray-500 mb-1">饮食禁忌</p>
                <p class="font-bold text-red-500 text-lg" id="almanacTaboo">
                  生冷、油腻
                </p>
              </div>
            </div>
            <div class="space-y-4">
              <div>
                <h5 class="text-sm font-medium text-gray-500 mb-2">今日卦象</h5>
                <div class="p-3 border rounded-lg text-center">
                  <p class="font-bold text-gray-800" id="almanacHexagram">
                    乾为天 (上上卦)
                  </p>
                  <p
                    class="text-xs text-gray-600 mt-1"
                    id="almanacHexagramDesc"
                  >
                    刚健中正，自强不息，饮食宜清淡，忌过咸过辣
                  </p>
                </div>
              </div>
              <div>
                <h5 class="text-sm font-medium text-gray-500 mb-2">时辰吉凶</h5>
                <div class="grid grid-cols-3 gap-2 text-center">
                  <div
                    class="p-2 bg-green-50 text-green-800 rounded-lg text-xs"
                  >
                    <p class="font-medium">辰时</p>
                    <p>7:00-9:00</p>
                    <p class="font-bold">吉</p>
                  </div>
                  <div class="p-2 bg-red-50 text-red-800 rounded-lg text-xs">
                    <p class="font-medium">巳时</p>
                    <p>9:00-11:00</p>
                    <p class="font-bold">凶</p>
                  </div>
                  <div
                    class="p-2 bg-green-50 text-green-800 rounded-lg text-xs"
                  >
                    <p class="font-medium">午时</p>
                    <p>11:00-13:00</p>
                    <p class="font-bold">吉</p>
                  </div>
                </div>
              </div>
              <div>
                <h5 class="text-sm font-medium text-gray-500 mb-2">今日运势</h5>
                <div class="p-3 border rounded-lg">
                  <div class="flex justify-between items-center mb-2">
                    <span class="text-xs text-gray-600"> 财运 </span>
                    <div class="flex">
                      <i class="fas fa-star text-yellow-400 text-xs"> </i>
                      <i class="fas fa-star text-yellow-400 text-xs"> </i>
                      <i class="fas fa-star text-yellow-400 text-xs"> </i>
                      <i class="fas fa-star text-yellow-400 text-xs"> </i>
                      <i class="far fa-star text-gray-300 text-xs"> </i>
                    </div>
                  </div>
                  <div class="flex justify-between items-center mb-2">
                    <span class="text-xs text-gray-600"> 食运 </span>
                    <div class="flex">
                      <i class="fas fa-star text-yellow-400 text-xs"> </i>
                      <i class="fas fa-star text-yellow-400 text-xs"> </i>
                      <i class="fas fa-star text-yellow-400 text-xs"> </i>
                      <i class="fas fa-star text-yellow-400 text-xs"> </i>
                      <i class="fas fa-star text-yellow-400 text-xs"> </i>
                    </div>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-600"> 健康运 </span>
                    <div class="flex">
                      <i class="fas fa-star text-yellow-400 text-xs"> </i>
                      <i class="fas fa-star text-yellow-400 text-xs"> </i>
                      <i class="fas fa-star text-yellow-400 text-xs"> </i>
                      <i class="far fa-star text-gray-300 text-xs"> </i>
                      <i class="far fa-star text-gray-300 text-xs"> </i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-6 p-4 bg-primary/5 rounded-xl text-center">
              <p class="text-sm text-gray-700 italic" id="almanacAdvice">
                今日天地气交，宜食温热，忌生冷。午餐宜早不宜晚，与日同行，可得天地之正气。
              </p>
            </div>
          </div>
        </div>
      </div>
      // 页面加载完成后初始化 document.addEventListener('DOMContentLoaded', ()
      => { // 获取DOM元素 const navbar = document.getElementById('navbar');
      const randomBtn = document.getElementById('randomBtn'); const foodResult =
      document.getElementById('foodResult'); const foodCategory =
      document.getElementById('foodCategory'); const historyBtn =
      document.getElementById('historyBtn'); const historyModal =
      document.getElementById('historyModal'); const closeHistoryBtn =
      document.getElementById('closeHistoryBtn'); const settingsBtn =
      document.getElementById('settingsBtn'); const settingsModal =
      document.getElementById('settingsModal'); // 页面相关元素 const pages =
      document.querySelectorAll('.page'); const navLinks =
      document.querySelectorAll('nav a'); const homeLink =
      document.querySelector('nav a:nth-child(1)'); const discoverLink =
      document.querySelector('nav a:nth-child(2)'); const favoriteLink =
      document.querySelector('nav a:nth-child(3)'); const profileLink =
      document.querySelector('nav a:nth-child(4)'); const goDiscoverBtn =
      document.getElementById('goDiscoverBtn'); // 页面切换函数 function
      showPage(pageId) { pages.forEach(page => { page.classList.add('hidden');
      page.classList.remove('active'); });
      document.getElementById(pageId).classList.remove('hidden');
      document.getElementById(pageId).classList.add('active'); //
      更新导航栏激活状态 navLinks.forEach(link => {
      link.classList.remove('text-primary');
      link.classList.add('text-gray-400'); }); } // 导航栏事件监听
      homeLink.addEventListener('click', () => { showPage('homePage');
      homeLink.classList.remove('text-gray-400');
      homeLink.classList.add('text-primary'); });
      discoverLink.addEventListener('click', () => { showPage('discoverPage');
      discoverLink.classList.remove('text-gray-400');
      discoverLink.classList.add('text-primary'); initFoodTrendChart(); });
      favoriteLink.addEventListener('click', () => { showPage('favoritePage');
      favoriteLink.classList.remove('text-gray-400');
      favoriteLink.classList.add('text-primary'); });
      profileLink.addEventListener('click', () => { showPage('profilePage');
      profileLink.classList.remove('text-gray-400');
      profileLink.classList.add('text-primary'); updateProfileStats(); }); //
      去发现按钮事件 if (goDiscoverBtn) {
      goDiscoverBtn.addEventListener('click', () => { showPage('discoverPage');
      discoverLink.classList.remove('text-gray-400');
      discoverLink.classList.add('text-primary'); initFoodTrendChart(); }); }
      const closeSettingsBtn = document.getElementById('closeSettingsBtn');
      const almanacBtn = document.getElementById('almanacBtn'); const
      almanacModal = document.getElementById('almanacModal'); const
      closeAlmanacBtn = document.getElementById('closeAlmanacBtn'); //
      监听滚动事件，改变导航栏样式 window.addEventListener('scroll', () => { if
      (window.scrollY > 10) { navbar.classList.add('py-2', 'shadow');
      navbar.classList.remove('py-3'); } else { navbar.classList.add('py-3');
      navbar.classList.remove('py-2', 'shadow'); } }); // 食物数据 const
      foodItems = [ { name: '火锅', category: '中餐' }, { name: '汉堡',
      category: '西餐' }, { name: '寿司', category: '日料' }, { name: '炸鸡',
      category: '小吃' }, { name: '披萨', category: '西餐' }, { name: '牛肉面',
      category: '中餐' }, { name: '烤肉', category: '韩式' }, { name: '麻辣烫',
      category: '小吃' }, { name: '奶茶', category: '饮品' }, { name: '蛋糕',
      category: '甜点' }, { name: '饺子', category: '中餐' }, { name: ' tacos',
      category: '墨西哥' }, { name: '意面', category: '西餐' }, { name: '炒饭',
      category: '中餐' }, { name: '冰淇淋', category: '甜点' } ]; //
      随机选择食物 let isSpinning = false; randomBtn.addEventListener('click',
      () => { if (isSpinning) return; isSpinning = true; randomBtn.disabled =
      true; randomBtn.innerHTML = `
      <i class="fas fa-spinner fa-spin mr-2"> </i>
      选择中... `; // 随机滚动效果 const spinCount = Math.floor(Math.random() *
      10) + 15; let count = 0; const spinInterval = setInterval(() => { const
      randomIndex = Math.floor(Math.random() * foodItems.length);
      foodResult.textContent = foodItems[randomIndex].name;
      foodCategory.textContent = foodItems[randomIndex].category; count++; if
      (count >= spinCount) { clearInterval(spinInterval); isSpinning = false;
      randomBtn.disabled = false; randomBtn.innerHTML = `
      <i class="fas fa-random mr-2"> </i>
      再选一次 `; // 添加到历史记录的逻辑可以在这里添加 } }, 100); }); //
      历史记录弹窗控制 historyBtn.addEventListener('click', () => {
      historyModal.classList.remove('hidden'); });
      closeHistoryBtn.addEventListener('click', () => {
      historyModal.classList.add('hidden'); }); // 设置弹窗控制
      settingsBtn.addEventListener('click', () => {
      settingsModal.classList.remove('hidden'); });
      closeSettingsBtn.addEventListener('click', () => {
      settingsModal.classList.add('hidden'); }); // 点击弹窗外部关闭弹窗
      historyModal.addEventListener('click', (e) => { if (e.target ===
      historyModal) { historyModal.classList.add('hidden'); } });
      settingsModal.addEventListener('click', (e) => { if (e.target ===
      settingsModal) { settingsModal.classList.add('hidden'); } }); //
      黄历弹窗控制 almanacBtn.addEventListener('click', () => {
      almanacModal.classList.remove('hidden'); updateAlmanacData(); });
      closeAlmanacBtn.addEventListener('click', () => {
      almanacModal.classList.add('hidden'); });
      almanacModal.addEventListener('click', (e) => { if (e.target ===
      almanacModal) { almanacModal.classList.add('hidden'); } }); //
      分类按钮点击效果 const categoryBtns =
      document.querySelectorAll('.category-btn'); categoryBtns.forEach(btn => {
      btn.addEventListener('click', () => { // 这里可以添加分类筛选逻辑
      btn.classList.add('scale-95'); setTimeout(() => {
      btn.classList.remove('scale-95'); }, 200); }); }); // 初始化美食趋势图表
      function initFoodTrendChart() { const chartDom =
      document.getElementById('foodTrendChart'); if (!chartDom) return; const
      myChart = echarts.init(chartDom); const option = { title: { text:
      '本月美食趋势', left: 'center', textStyle: { fontSize: 14, fontWeight:
      'normal' } }, tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' }
      }, grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
      xAxis: { type: 'category', data: ['火锅', '奶茶', '寿司', '烤肉', '披萨',
      '炸鸡'], axisLabel: { fontSize: 12 } }, yAxis: { type: 'value', axisLabel:
      { fontSize: 12 } }, series: [ { data: [120, 200, 150, 80, 70, 110], type:
      'bar', itemStyle: { color: function(params) { const colorList =
      ['#FF6B35', '#FFD166', '#06D6A0', '#118AB2', '#6B7280', '#9333EA']; return
      colorList[params.dataIndex]; } }, emphasis: { itemStyle: { shadowBlur: 10,
      shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.3)' } } } ] }; option &&
      myChart.setOption(option); // 窗口大小变化时重绘图表
      window.addEventListener('resize', () => { myChart.resize(); }); } //
      更新个人资料统计数据 function updateProfileStats() { const historyItems =
      document.querySelectorAll('#historyModal .space-y-4 > div'); const
      historyCount = document.getElementById('historyCount'); if (historyCount)
      { historyCount.textContent = historyItems.length; } const favoriteCount =
      document.getElementById('favoriteCount'); if (favoriteCount) {
      favoriteCount.textContent = '0'; // 实际应用中应该从本地存储获取真实数据 }
      } // 更新黄历数据 function updateAlmanacData() { const now = new Date();
      const year = now.getFullYear(); const month = now.getMonth() + 1; const
      day = now.getDate(); const weekDays = ['日', '一', '二', '三', '四', '五',
      '六']; const weekDay = weekDays[now.getDay()]; // 更新日期
      document.getElementById('almanacDate').textContent =
      `${year}年${month}月${day}日 星期${weekDay}`; //
      简单模拟农历日期（实际应用中应使用农历库） const lunarMonths = ['正',
      '二', '三', '四', '五', '六', '七', '八', '九', '十', '冬', '腊']; const
      lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七',
      '初八', '初九', '初十', '十一', '十二', '十三', '十四', '十五', '十六',
      '十七', '十八', '十九', '二十', '廿一', '廿二', '廿三', '廿四', '廿五',
      '廿六', '廿七', '廿八', '廿九', '三十']; const lunarMonth =
      lunarMonths[now.getMonth()]; const lunarDay = lunarDays[day - 1];
      document.getElementById('almanacLunar').textContent = `癸卯年
      ${lunarMonth}月${lunarDay}`; //
      根据日期生成随机卦象建议（实际应用中应使用易经算法） const hexagrams = [
      "乾为天 (上上卦)", "坤为地 (上上卦)", "水雷屯 (下下卦)", "山水蒙
      (中下卦)", "水天需 (中上卦)", "天水讼 (中下卦)", "地水师 (中下卦)",
      "水地比 (上上卦)", "风天小畜 (中上卦)", "天泽履 (中上卦)", "地天泰
      (上上卦)", "天地否 (中中卦)" ]; const randomHex = Math.floor(Math.random()
      * hexagrams.length);
      document.getElementById('almanacHexagram').textContent =
      hexagrams[randomHex]; // 根据季节更新宜食建议 const seasons = ['春季',
      '夏季', '秋季', '冬季']; const season = seasons[Math.floor(now.getMonth()
      / 3)]; const seasonalFood = { '春季': '芽类蔬菜、春笋、韭菜', '夏季':
      '清热食物、瓜类、绿豆', '秋季': '润肺食物、白色食物、坚果', '冬季':
      '温补食材、根茎类、羊肉' }; const seasonalTaboo = { '春季': '生冷、油腻',
      '夏季': '辛辣、燥热', '秋季': '辛辣、油炸', '冬季': '生冷、寒凉' };
      document.getElementById('almanacFood').textContent = seasonalFood[season];
      document.getElementById('almanacTaboo').textContent =
      seasonalTaboo[season]; // 更新节气（简化版） const solarTerms = [ {month:
      2, day: 4, term: "立春"}, {month: 2, day: 19, term: "雨水"}, {month: 3,
      day: 6, term: "惊蛰"}, {month: 3, day: 21, term: "春分"}, {month: 4, day:
      5, term: "清明"}, {month: 4, day: 20, term: "谷雨"}, {month: 5, day: 6,
      term: "立夏"}, {month: 5, day: 21, term: "小满"}, {month: 6, day: 6, term:
      "芒种"}, {month: 6, day: 21, term: "夏至"}, {month: 7, day: 7, term:
      "小暑"}, {month: 7, day: 23, term: "大暑"}, {month: 8, day: 7, term:
      "立秋"}, {month: 8, day: 23, term: "处暑"}, {month: 9, day: 7, term:
      "白露"}, {month: 9, day: 23, term: "秋分"}, {month: 10, day: 8, term:
      "寒露"}, {month: 10, day: 23, term: "霜降"}, {month: 11, day: 7, term:
      "立冬"}, {month: 11, day: 22, term: "小雪"}, {month: 12, day: 7, term:
      "大雪"}, {month: 12, day: 22, term: "冬至"}, {month: 1, day: 5, term:
      "小寒"}, {month: 1, day: 20, term: "大寒"} ]; const todayTerm =
      solarTerms.find(term => term.month === now.getMonth() + 1 &&
      Math.abs(term.day - day) <= 2); if (todayTerm) {
      document.getElementById('almanacSolarTerm').textContent = todayTerm.term;
      } else { document.getElementById('almanacSolarTerm').textContent =
      `${season}时节`; } } });
    </main>
  </body>
</html>
