// 云函数：创建点餐记录
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  try {
    const { dishId, note = '' } = event;
    const { OPENID } = cloud.getWXContext();

    if (!OPENID) {
      return {
        code: -1,
        message: '用户身份验证失败'
      };
    }

    if (!dishId) {
      return {
        code: -1,
        message: '菜品ID不能为空'
      };
    }

    // 获取菜品信息
    const dishResult = await db.collection('dishes')
      .doc(dishId)
      .get();

    if (!dishResult.data) {
      return {
        code: -1,
        message: '菜品不存在'
      };
    }

    const dish = dishResult.data;

    // 获取用户信息
    const userResult = await db.collection('users')
      .where({
        openid: OPENID
      })
      .get();

    if (userResult.data.length === 0) {
      return {
        code: -1,
        message: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 创建点餐记录
    const orderData = {
      userId: user._id,
      dishId: dishId,
      dishName: dish.name,
      dishImage: dish.image,
      cookTime: dish.cookTime,
      difficulty: dish.difficulty,
      note: note,
      status: 'pending',
      orderTime: new Date(),
      rating: null,
      comment: ''
    };

    const orderResult = await db.collection('orders').add({
      data: orderData
    });

    // 更新菜品点餐次数
    await db.collection('dishes')
      .doc(dishId)
      .update({
        data: {
          orderCount: db.command.inc(1)
        }
      });

    return {
      code: 0,
      message: '点餐成功',
      data: {
        orderId: orderResult._id,
        ...orderData
      }
    };

  } catch (error) {
    console.error('创建点餐记录失败:', error);
    return {
      code: -1,
      message: '点餐失败',
      error: error.message
    };
  }
};
