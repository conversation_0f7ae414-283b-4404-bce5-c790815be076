// 云函数：获取菜品详情
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  try {
    const { dishId } = event;

    if (!dishId) {
      return {
        code: -1,
        message: '菜品ID不能为空'
      };
    }

    // 获取菜品详情
    const result = await db.collection('dishes')
      .doc(dishId)
      .get();

    if (!result.data) {
      return {
        code: -1,
        message: '菜品不存在'
      };
    }

    // 增加浏览次数（可选）
    try {
      await db.collection('dishes')
        .doc(dishId)
        .update({
          data: {
            viewCount: db.command.inc(1)
          }
        });
    } catch (error) {
      console.log('更新浏览次数失败:', error);
    }

    return {
      code: 0,
      message: '获取菜品详情成功',
      data: result.data
    };

  } catch (error) {
    console.error('获取菜品详情失败:', error);
    return {
      code: -1,
      message: '获取菜品详情失败',
      error: error.message
    };
  }
};
