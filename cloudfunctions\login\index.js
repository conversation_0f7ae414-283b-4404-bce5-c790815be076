// 云函数：用户登录
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  try {
    const { userInfo } = event;
    const { OPENID } = cloud.getWXContext();

    if (!OPENID) {
      return {
        code: -1,
        message: '获取用户身份失败'
      };
    }

    // 查询用户是否已存在
    const existingUser = await db.collection('users')
      .where({
        openid: OPENID
      })
      .get();

    let userData;

    if (existingUser.data.length > 0) {
      // 用户已存在，更新信息
      userData = existingUser.data[0];
      
      // 更新用户信息
      await db.collection('users')
        .doc(userData._id)
        .update({
          data: {
            nickname: userInfo.nickName,
            avatar: userInfo.avatarUrl,
            lastLoginTime: new Date()
          }
        });

      userData.nickname = userInfo.nickName;
      userData.avatar = userInfo.avatarUrl;
      userData.lastLoginTime = new Date();

    } else {
      // 新用户，创建记录
      const newUser = {
        openid: OPENID,
        nickname: userInfo.nickName,
        avatar: userInfo.avatarUrl,
        familyId: null,
        role: 'member',
        preferences: {
          spicyLevel: 3,
          sweetLevel: 3,
          favoriteCuisine: []
        },
        createTime: new Date(),
        lastLoginTime: new Date()
      };

      const result = await db.collection('users').add({
        data: newUser
      });

      userData = {
        ...newUser,
        _id: result._id
      };
    }

    return {
      code: 0,
      message: '登录成功',
      data: {
        _id: userData._id,
        openid: userData.openid,
        nickname: userData.nickname,
        avatar: userData.avatar,
        familyId: userData.familyId,
        role: userData.role,
        preferences: userData.preferences
      }
    };

  } catch (error) {
    console.error('用户登录失败:', error);
    return {
      code: -1,
      message: '登录失败',
      error: error.message
    };
  }
};
