// 云函数：初始化数据库
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 示例菜品数据
const sampleDishes = [
  {
    name: '宫保鸡丁',
    category: 'sichuan',
    image: 'https://example.com/gongbao.jpg',
    description: '经典川菜，酸甜微辣，鸡肉嫩滑，花生香脆',
    difficulty: 3,
    cookTime: 25,
    rating: 4.5,
    orderCount: 156,
    ingredients: [
      { name: '鸡胸肉', amount: '300', unit: '克' },
      { name: '花生米', amount: '50', unit: '克' },
      { name: '干辣椒', amount: '10', unit: '个' },
      { name: '花椒', amount: '1', unit: '茶匙' },
      { name: '葱', amount: '2', unit: '根' },
      { name: '姜', amount: '1', unit: '块' },
      { name: '蒜', amount: '3', unit: '瓣' }
    ],
    steps: [
      {
        stepNumber: 1,
        description: '鸡胸肉切丁，用料酒、生抽、淀粉腌制15分钟',
        image: ''
      },
      {
        stepNumber: 2,
        description: '花生米过油炸至金黄，捞出备用',
        image: ''
      },
      {
        stepNumber: 3,
        description: '热锅下油，爆炒干辣椒和花椒至香味四溢',
        image: ''
      },
      {
        stepNumber: 4,
        description: '下鸡丁炒至变色，加入葱姜蒜爆炒',
        image: ''
      },
      {
        stepNumber: 5,
        description: '调入宫保汁炒匀，最后加入花生米翻炒即可',
        image: ''
      }
    ],
    tags: ['川菜', '下饭菜', '家常菜'],
    nutrition: {
      calories: '280',
      protein: '25g',
      carbs: '12g'
    },
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: '红烧肉',
    category: 'shandong',
    image: 'https://example.com/hongshaorou.jpg',
    description: '色泽红亮，肥而不腻，入口即化的经典家常菜',
    difficulty: 2,
    cookTime: 90,
    rating: 4.8,
    orderCount: 203,
    ingredients: [
      { name: '五花肉', amount: '500', unit: '克' },
      { name: '冰糖', amount: '30', unit: '克' },
      { name: '生抽', amount: '2', unit: '汤匙' },
      { name: '老抽', amount: '1', unit: '汤匙' },
      { name: '料酒', amount: '2', unit: '汤匙' },
      { name: '葱', amount: '2', unit: '根' },
      { name: '姜', amount: '3', unit: '片' }
    ],
    steps: [
      {
        stepNumber: 1,
        description: '五花肉切块，冷水下锅焯水去腥',
        image: ''
      },
      {
        stepNumber: 2,
        description: '锅内放少量油，下冰糖炒糖色至焦糖色',
        image: ''
      },
      {
        stepNumber: 3,
        description: '下肉块翻炒上色，加入葱姜爆香',
        image: ''
      },
      {
        stepNumber: 4,
        description: '加入生抽、老抽、料酒炒匀',
        image: ''
      },
      {
        stepNumber: 5,
        description: '加开水没过肉块，大火烧开转小火炖1小时',
        image: ''
      },
      {
        stepNumber: 6,
        description: '大火收汁至浓稠即可',
        image: ''
      }
    ],
    tags: ['鲁菜', '下饭菜', '家常菜', '荤菜'],
    nutrition: {
      calories: '450',
      protein: '20g',
      carbs: '8g'
    },
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: '西红柿鸡蛋',
    category: 'all',
    image: 'https://example.com/xihongshi.jpg',
    description: '简单易做的家常菜，酸甜开胃，营养丰富',
    difficulty: 1,
    cookTime: 15,
    rating: 4.3,
    orderCount: 89,
    ingredients: [
      { name: '西红柿', amount: '3', unit: '个' },
      { name: '鸡蛋', amount: '4', unit: '个' },
      { name: '葱', amount: '1', unit: '根' },
      { name: '糖', amount: '1', unit: '茶匙' },
      { name: '盐', amount: '适量', unit: '' }
    ],
    steps: [
      {
        stepNumber: 1,
        description: '西红柿划十字刀，开水烫后去皮切块',
        image: ''
      },
      {
        stepNumber: 2,
        description: '鸡蛋打散，加少量盐调味',
        image: ''
      },
      {
        stepNumber: 3,
        description: '热锅下油，炒鸡蛋至半熟盛起',
        image: ''
      },
      {
        stepNumber: 4,
        description: '锅内留底油，下西红柿炒出汁水',
        image: ''
      },
      {
        stepNumber: 5,
        description: '加糖和盐调味，倒入鸡蛋炒匀即可',
        image: ''
      }
    ],
    tags: ['家常菜', '素菜', '简单'],
    nutrition: {
      calories: '180',
      protein: '12g',
      carbs: '8g'
    },
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: '麻婆豆腐',
    category: 'sichuan',
    image: 'https://example.com/mapo.jpg',
    description: '麻辣鲜香，豆腐嫩滑，经典川菜代表',
    difficulty: 2,
    cookTime: 20,
    rating: 4.6,
    orderCount: 134,
    ingredients: [
      { name: '嫩豆腐', amount: '400', unit: '克' },
      { name: '牛肉末', amount: '100', unit: '克' },
      { name: '豆瓣酱', amount: '2', unit: '汤匙' },
      { name: '花椒粉', amount: '1', unit: '茶匙' },
      { name: '葱', amount: '2', unit: '根' },
      { name: '蒜', amount: '3', unit: '瓣' }
    ],
    steps: [
      {
        stepNumber: 1,
        description: '豆腐切块，用盐水焯一下去豆腥味',
        image: ''
      },
      {
        stepNumber: 2,
        description: '热锅下油，炒牛肉末至变色',
        image: ''
      },
      {
        stepNumber: 3,
        description: '加豆瓣酱炒出红油，下蒜末爆香',
        image: ''
      },
      {
        stepNumber: 4,
        description: '加水烧开，下豆腐块轻轻推匀',
        image: ''
      },
      {
        stepNumber: 5,
        description: '用水淀粉勾芡，撒花椒粉和葱花即可',
        image: ''
      }
    ],
    tags: ['川菜', '下饭菜', '豆制品'],
    nutrition: {
      calories: '220',
      protein: '18g',
      carbs: '6g'
    },
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: '蒸蛋羹',
    category: 'all',
    image: 'https://example.com/zhengdan.jpg',
    description: '嫩滑如布丁，营养丰富，老少皆宜',
    difficulty: 1,
    cookTime: 12,
    rating: 4.2,
    orderCount: 67,
    ingredients: [
      { name: '鸡蛋', amount: '3', unit: '个' },
      { name: '温水', amount: '150', unit: '毫升' },
      { name: '盐', amount: '少许', unit: '' },
      { name: '香油', amount: '几滴', unit: '' }
    ],
    steps: [
      {
        stepNumber: 1,
        description: '鸡蛋打散，加盐调味',
        image: ''
      },
      {
        stepNumber: 2,
        description: '加入温水搅拌均匀，过筛去泡沫',
        image: ''
      },
      {
        stepNumber: 3,
        description: '蒙上保鲜膜，用牙签扎几个小孔',
        image: ''
      },
      {
        stepNumber: 4,
        description: '水开后蒸8-10分钟至凝固',
        image: ''
      },
      {
        stepNumber: 5,
        description: '出锅后滴几滴香油即可',
        image: ''
      }
    ],
    tags: ['蒸菜', '简单', '营养'],
    nutrition: {
      calories: '140',
      protein: '12g',
      carbs: '2g'
    },
    createTime: new Date(),
    updateTime: new Date()
  }
];

exports.main = async (event, context) => {
  try {
    // 创建数据库集合
    const collections = ['dishes', 'users', 'orders', 'favorites'];
    
    for (const collectionName of collections) {
      try {
        await db.createCollection(collectionName);
        console.log(`集合 ${collectionName} 创建成功`);
      } catch (error) {
        if (error.errCode === -1) {
          console.log(`集合 ${collectionName} 已存在`);
        } else {
          console.error(`创建集合 ${collectionName} 失败:`, error);
        }
      }
    }

    // 检查是否已有数据
    const existingDishes = await db.collection('dishes').count();
    
    if (existingDishes.total === 0) {
      // 插入示例菜品数据
      const dishResults = await db.collection('dishes').add({
        data: sampleDishes
      });
      console.log('示例菜品数据插入成功:', dishResults);
    } else {
      console.log('菜品数据已存在，跳过初始化');
    }

    // 创建索引
    try {
      await db.collection('dishes').createIndex({
        keys: {
          category: 1,
          rating: -1,
          orderCount: -1,
          createTime: -1
        }
      });
      console.log('菜品索引创建成功');
    } catch (error) {
      console.log('索引可能已存在:', error.message);
    }

    return {
      code: 0,
      message: '数据库初始化成功',
      data: {
        collections: collections,
        dishCount: sampleDishes.length
      }
    };

  } catch (error) {
    console.error('数据库初始化失败:', error);
    return {
      code: -1,
      message: '数据库初始化失败',
      error: error.message
    };
  }
};
