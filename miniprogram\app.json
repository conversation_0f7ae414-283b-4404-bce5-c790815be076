{"pages": ["pages/test-cloud/test-cloud", "pages/index/index", "pages/dish-list/dish-list", "pages/dish-detail/dish-detail", "pages/cooking-steps/cooking-steps", "pages/order-history/order-history", "pages/favorites/favorites", "pages/search/search", "pages/profile/profile", "pages/settings/settings", "pages/family/family"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#FF6B35", "navigationBarTitleText": "家庭点餐", "navigationBarTextStyle": "white", "backgroundColor": "#F8F9FA"}, "tabBar": {"color": "#999999", "selectedColor": "#FF6B35", "backgroundColor": "#FFFFFF", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "首页"}, {"pagePath": "pages/dish-list/dish-list", "text": "菜品"}, {"pagePath": "pages/order-history/order-history", "text": "记录"}, {"pagePath": "pages/favorites/favorites", "text": "收藏"}, {"pagePath": "pages/profile/profile", "text": "我的"}]}, "networkTimeout": {"request": 10000, "downloadFile": 10000}, "debug": false, "navigateToMiniProgramAppIdList": [], "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredPrivateInfos": [], "lazyCodeLoading": "requiredComponents", "sitemapLocation": "sitemap.json"}