{"pages": ["pages/index/index", "pages/dish-list/dish-list", "pages/dish-detail/dish-detail", "pages/cooking-steps/cooking-steps", "pages/order-history/order-history", "pages/favorites/favorites", "pages/search/search", "pages/profile/profile", "pages/settings/settings", "pages/family/family"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#FF6B35", "navigationBarTitleText": "家庭点餐", "navigationBarTextStyle": "white", "backgroundColor": "#F8F9FA"}, "tabBar": {"color": "#999999", "selectedColor": "#FF6B35", "backgroundColor": "#FFFFFF", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "images/tab/home.png", "selectedIconPath": "images/tab/home-active.png"}, {"pagePath": "pages/dish-list/dish-list", "text": "菜品", "iconPath": "images/tab/dish.png", "selectedIconPath": "images/tab/dish-active.png"}, {"pagePath": "pages/order-history/order-history", "text": "记录", "iconPath": "images/tab/order.png", "selectedIconPath": "images/tab/order-active.png"}, {"pagePath": "pages/favorites/favorites", "text": "收藏", "iconPath": "images/tab/favorite.png", "selectedIconPath": "images/tab/favorite-active.png"}, {"pagePath": "pages/profile/profile", "text": "我的", "iconPath": "images/tab/profile.png", "selectedIconPath": "images/tab/profile-active.png"}]}, "networkTimeout": {"request": 10000, "downloadFile": 10000}, "debug": false, "navigateToMiniProgramAppIdList": [], "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredPrivateInfos": [], "lazyCodeLoading": "requiredComponents", "sitemapLocation": "sitemap.json"}