// pages/test-cloud/test-cloud.js
Page({
  data: {
    testResult: '点击按钮开始测试...'
  },

  // 测试云开发初始化
  testCloudInit() {
    this.setData({ testResult: '正在测试云开发初始化...' });
    
    try {
      if (!wx.cloud) {
        this.setData({ testResult: '❌ 云开发不可用，请检查基础库版本' });
        return;
      }
      
      // 尝试获取云开发环境信息
      const env = wx.cloud.env;
      this.setData({ 
        testResult: `✅ 云开发初始化成功\n当前环境: ${env || '默认环境'}` 
      });
    } catch (error) {
      this.setData({ 
        testResult: `❌ 云开发初始化失败: ${error.message}` 
      });
    }
  },

  // 初始化数据库
  initDatabase() {
    this.setData({ testResult: '正在初始化数据库...' });
    
    wx.cloud.callFunction({
      name: 'initDatabase',
      success: (res) => {
        console.log('数据库初始化结果:', res);
        if (res.result.code === 0) {
          this.setData({ 
            testResult: `✅ 数据库初始化成功\n创建集合: ${res.result.data.collections.join(', ')}\n示例菜品数量: ${res.result.data.dishCount}` 
          });
        } else {
          this.setData({ 
            testResult: `❌ 数据库初始化失败: ${res.result.message}` 
          });
        }
      },
      fail: (error) => {
        console.error('数据库初始化失败:', error);
        this.setData({ 
          testResult: `❌ 数据库初始化失败: ${error.errMsg}` 
        });
      }
    });
  },

  // 测试获取菜品列表
  testGetDishes() {
    this.setData({ testResult: '正在测试获取菜品列表...' });
    
    wx.cloud.callFunction({
      name: 'getDishes',
      data: {
        limit: 3
      },
      success: (res) => {
        console.log('获取菜品结果:', res);
        if (res.result.code === 0) {
          const dishes = res.result.data.dishes;
          const dishNames = dishes.map(dish => dish.name).join(', ');
          this.setData({ 
            testResult: `✅ 获取菜品列表成功\n菜品数量: ${dishes.length}\n菜品名称: ${dishNames}` 
          });
        } else {
          this.setData({ 
            testResult: `❌ 获取菜品列表失败: ${res.result.message}` 
          });
        }
      },
      fail: (error) => {
        console.error('获取菜品失败:', error);
        this.setData({ 
          testResult: `❌ 获取菜品列表失败: ${error.errMsg}` 
        });
      }
    });
  },

  // 测试用户登录
  testLogin() {
    this.setData({ testResult: '正在测试用户登录...' });
    
    // 模拟用户信息
    const mockUserInfo = {
      nickName: '测试用户',
      avatarUrl: 'https://example.com/avatar.jpg'
    };
    
    wx.cloud.callFunction({
      name: 'login',
      data: {
        userInfo: mockUserInfo
      },
      success: (res) => {
        console.log('用户登录结果:', res);
        if (res.result.code === 0) {
          this.setData({ 
            testResult: `✅ 用户登录成功\n用户昵称: ${res.result.data.nickname}\n用户角色: ${res.result.data.role}` 
          });
        } else {
          this.setData({ 
            testResult: `❌ 用户登录失败: ${res.result.message}` 
          });
        }
      },
      fail: (error) => {
        console.error('用户登录失败:', error);
        this.setData({ 
          testResult: `❌ 用户登录失败: ${error.errMsg}` 
        });
      }
    });
  }
});
