/* pages/dish-list/dish-list.wxss */

.filter-bar {
  display: flex;
  background-color: #FFFFFF;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.filter-item {
  display: flex;
  align-items: center;
  padding: 15rpx 30rpx;
  margin-right: 20rpx;
  background-color: #F8F9FA;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #333333;
}

.filter-arrow {
  margin-left: 10rpx;
  font-size: 24rpx;
  color: #999999;
}

.dish-list {
  padding: 0 20rpx;
}

.dish-item {
  display: flex;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.dish-image {
  width: 200rpx;
  height: 150rpx;
  flex-shrink: 0;
}

.dish-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.dish-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.dish-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dish-meta {
  display: flex;
  margin-bottom: 15rpx;
}

.meta-item {
  font-size: 24rpx;
  color: #999999;
  margin-right: 20rpx;
}

.dish-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  font-size: 22rpx;
  color: #FF6B35;
  background-color: rgba(255, 107, 53, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 10rpx;
  margin-bottom: 5rpx;
}

.dish-actions {
  display: flex;
  align-items: center;
  padding: 20rpx;
}

.btn-order {
  background-color: #FF6B35;
  color: #FFFFFF;
  border: none;
  border-radius: 50rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
}

.loading-more {
  text-align: center;
  padding: 40rpx;
  color: #999999;
}

.no-more {
  text-align: center;
  padding: 40rpx;
  color: #999999;
  font-size: 26rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999999;
}
