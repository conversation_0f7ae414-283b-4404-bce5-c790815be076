// pages/index/index.js
const { dishAPI, statsAPI } = require('../../utils/api');
const { DISH_CATEGORIES, DIFFICULTY_LEVELS } = require('../../utils/constants');
const { formatTime, getRelativeTime } = require('../../utils/util');

Page({
  data: {
    // 分类数据
    categories: DISH_CATEGORIES,
    selectedCategory: 'all',
    
    // 菜品数据
    recommendedDishes: [],
    popularDishes: [],
    recentDishes: [],
    
    // 状态
    loading: false,
    isEmpty: false
  },

  onLoad(options) {
    // 页面加载时初始化数据
    this.initData();
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData();
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    // 上拉加载更多（如果需要的话）
  },

  /**
   * 初始化数据
   */
  async initData() {
    try {
      this.setData({ loading: true });
      
      // 并行加载所有数据
      const [recommended, popular, recent] = await Promise.all([
        this.loadRecommendedDishes(),
        this.loadPopularDishes(),
        this.loadRecentDishes()
      ]);

      this.setData({
        recommendedDishes: recommended,
        popularDishes: popular,
        recentDishes: recent,
        isEmpty: recommended.length === 0 && popular.length === 0 && recent.length === 0
      });

    } catch (error) {
      console.error('初始化数据失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'error'
      });
      this.setData({ isEmpty: true });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    return this.initData();
  },

  /**
   * 加载推荐菜品
   */
  async loadRecommendedDishes() {
    try {
      const dishes = await dishAPI.getRecommendedDishes(6);
      return this.processDishData(dishes);
    } catch (error) {
      console.error('加载推荐菜品失败:', error);
      return [];
    }
  },

  /**
   * 加载热门菜品
   */
  async loadPopularDishes() {
    try {
      const result = await statsAPI.getPopularDishes(5);
      const dishes = result.dishes || result;
      return this.processDishData(dishes);
    } catch (error) {
      console.error('加载热门菜品失败:', error);
      return [];
    }
  },

  /**
   * 加载最近添加的菜品
   */
  async loadRecentDishes() {
    try {
      const dishes = await dishAPI.getDishes({
        sortBy: 'createTime',
        limit: 6
      });
      return this.processDishData(dishes.dishes || dishes);
    } catch (error) {
      console.error('加载最近菜品失败:', error);
      return [];
    }
  },

  /**
   * 处理菜品数据
   */
  processDishData(dishes) {
    if (!Array.isArray(dishes)) return [];
    
    return dishes.map(dish => {
      // 处理难度等级
      const difficultyConfig = DIFFICULTY_LEVELS.find(d => d.level === dish.difficulty) || DIFFICULTY_LEVELS[0];
      
      // 处理评分星级
      const rating = dish.rating || 0;
      const fullStars = Math.floor(rating);
      const hasHalfStar = rating - fullStars >= 0.5;
      let ratingStars = '⭐'.repeat(fullStars);
      if (hasHalfStar) ratingStars += '⭐';
      
      return {
        ...dish,
        difficultyText: difficultyConfig.name,
        ratingStars: ratingStars || '⭐',
        createTimeText: getRelativeTime(dish.createTime),
        orderCount: dish.orderCount || 0
      };
    });
  },

  /**
   * 搜索点击事件
   */
  onSearchTap() {
    wx.navigateTo({
      url: '/pages/search/search'
    });
  },

  /**
   * 分类点击事件
   */
  onCategoryTap(e) {
    const categoryId = e.currentTarget.dataset.id;
    this.setData({ selectedCategory: categoryId });
    
    // 跳转到菜品列表页面
    wx.navigateTo({
      url: `/pages/dish-list/dish-list?category=${categoryId}`
    });
  },

  /**
   * 菜品点击事件
   */
  onDishTap(e) {
    const dishId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/dish-detail/dish-detail?id=${dishId}`
    });
  },

  /**
   * 查看更多点击事件
   */
  onViewMoreTap(e) {
    const type = e.currentTarget.dataset.type;
    let url = '/pages/dish-list/dish-list';
    
    switch (type) {
      case 'recommended':
        url += '?type=recommended';
        break;
      case 'popular':
        url += '?sortBy=orderCount';
        break;
      case 'recent':
        url += '?sortBy=createTime';
        break;
    }
    
    wx.navigateTo({ url });
  },

  /**
   * 刷新按钮点击事件
   */
  onRefreshTap() {
    this.refreshData();
  }
});
