<!--pages/test-cloud/test-cloud.wxml-->
<view class="page-container">
  <view class="test-section">
    <text class="section-title">云开发配置测试</text>
    
    <button class="btn btn-primary btn-block mb-2" bindtap="testCloudInit">
      测试云开发初始化
    </button>
    
    <button class="btn btn-primary btn-block mb-2" bindtap="initDatabase">
      初始化数据库
    </button>
    
    <button class="btn btn-primary btn-block mb-2" bindtap="testGetDishes">
      测试获取菜品列表
    </button>
    
    <button class="btn btn-primary btn-block mb-2" bindtap="testLogin">
      测试用户登录
    </button>
    
    <view class="test-result">
      <text class="result-title">测试结果：</text>
      <text class="result-content">{{testResult}}</text>
    </view>
  </view>
</view>
