// pages/dish-list/dish-list.js
const { dishAPI } = require('../../utils/api');
const { SORT_OPTIONS, DIFFICULTY_LEVELS } = require('../../utils/constants');

Page({
  data: {
    dishes: [],
    currentSort: SORT_OPTIONS[0],
    loading: false,
    hasMore: true,
    isEmpty: false,
    page: 1,
    category: 'all'
  },

  onLoad(options) {
    if (options.category) {
      this.setData({ category: options.category });
    }
    this.loadDishes();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore();
    }
  },

  async loadDishes() {
    try {
      this.setData({ loading: true });
      const result = await dishAPI.getDishes({
        category: this.data.category,
        page: 1,
        limit: 20
      });

      const dishes = this.processDishData(result.dishes || []);
      this.setData({
        dishes: dishes,
        hasMore: result.hasMore || false,
        isEmpty: dishes.length === 0,
        page: 1
      });
    } catch (error) {
      console.error('加载菜品失败:', error);
      wx.showToast({ title: '加载失败', icon: 'error' });
    } finally {
      this.setData({ loading: false });
    }
  },

  async loadMore() {
    try {
      this.setData({ loading: true });
      const nextPage = this.data.page + 1;
      const result = await dishAPI.getDishes({
        category: this.data.category,
        page: nextPage,
        limit: 20
      });

      const newDishes = this.processDishData(result.dishes || []);
      this.setData({
        dishes: [...this.data.dishes, ...newDishes],
        hasMore: result.hasMore || false,
        page: nextPage
      });
    } catch (error) {
      console.error('加载更多失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },

  async refreshData() {
    await this.loadDishes();
    wx.stopPullDownRefresh();
  },

  processDishData(dishes) {
    return dishes.map(dish => {
      const difficultyConfig = DIFFICULTY_LEVELS.find(d => d.level === dish.difficulty) || DIFFICULTY_LEVELS[0];
      return {
        ...dish,
        difficultyText: difficultyConfig.name
      };
    });
  },

  onDishTap(e) {
    const dishId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/dish-detail/dish-detail?id=${dishId}`
    });
  },

  onOrderTap(e) {
    const dishId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/dish-detail/dish-detail?id=${dishId}&action=order`
    });
  },

  onSortTap() {
    wx.showActionSheet({
      itemList: SORT_OPTIONS.map(item => item.name),
      success: (res) => {
        const selectedSort = SORT_OPTIONS[res.tapIndex];
        this.setData({ currentSort: selectedSort });
        this.loadDishes();
      }
    });
  },

  onFilterTap() {
    wx.showToast({ title: '筛选功能开发中', icon: 'none' });
  }
});