# 家庭点餐微信小程序

一个专为家庭设计的点餐微信小程序，帮助家庭成员轻松选择想吃的菜品，并提供详细的制作指导。

## 项目特色

- 🏠 **家庭专用**：专为家庭内部使用设计
- 🍽️ **菜品管理**：丰富的菜品库和分类管理
- 📝 **制作指导**：详细的制作步骤和食材清单
- 📊 **智能推荐**：基于家庭偏好的个性化推荐
- 📱 **用户友好**：简洁直观的用户界面

## 技术架构

- **前端**：微信小程序原生开发
- **数据存储**：本地数据 + 本地存储
- **数据管理**：本地JSON数据文件
- **工具**：微信开发者工具

> **注意**：当前版本使用本地数据进行开发和测试，无需配置云开发环境。

## 项目结构

```
diancan/
├── docs/                    # 项目文档
│   ├── requirements.md      # 需求文档
│   ├── design.md           # 设计方案
│   └── tasks.md            # 任务文档
├── miniprogram/            # 小程序源码
│   ├── pages/              # 页面文件
│   │   ├── index/          # 首页
│   │   ├── dish-list/      # 菜品列表
│   │   ├── dish-detail/    # 菜品详情
│   │   ├── order-history/  # 点餐记录
│   │   ├── favorites/      # 收藏页面
│   │   ├── search/         # 搜索页面
│   │   └── profile/        # 个人中心
│   ├── data/               # 本地数据文件
│   │   ├── dishes.js       # 菜品数据
│   │   ├── users.js        # 用户数据
│   │   ├── orders.js       # 订单数据
│   │   └── favorites.js    # 收藏数据
│   ├── components/         # 组件文件
│   ├── utils/              # 工具类
│   │   ├── api.js          # API请求封装（本地数据版）
│   │   ├── constants.js    # 常量配置
│   │   └── util.js         # 通用工具函数
│   ├── images/             # 图片资源
│   ├── app.js              # 应用入口
│   ├── app.json            # 应用配置
│   └── app.wxss            # 全局样式
├── project.config.json     # 项目配置
└── README.md              # 项目说明
```

## 主要功能

### 已实现功能 ✅

1. **项目基础架构**
   - 微信小程序项目初始化
   - 页面路由配置
   - 全局样式和工具类
   - 本地数据API封装

2. **基础页面结构**
   - 首页布局和导航
   - 菜品列表页面
   - 菜品详情页面
   - 个人中心页面
   - 其他辅助页面

3. **本地数据系统**
   - 6道示例菜品数据
   - 完整的制作步骤和食材清单
   - 本地存储的用户数据
   - 点餐记录和收藏功能

### 开发中功能 🚧

1. **基础组件开发**
   - 通用组件库
   - 菜品卡片组件
   - 评分组件等

### 待开发功能 📋

1. **用户认证系统**
   - 微信登录集成
   - 用户信息管理
   - 权限控制

2. **菜品管理系统**
   - 菜品数据展示
   - 分类筛选功能
   - 搜索功能

3. **点餐核心功能**
   - 点餐操作
   - 点餐记录管理
   - 收藏功能

4. **制作指导功能**
   - 制作步骤展示
   - 计时器功能
   - 制作笔记

5. **数据持久化**
   - 云开发集成（可选）
   - 数据同步功能
   - 离线数据支持

## 开发进度

- [x] 项目初始化和环境搭建
- [x] 本地数据系统设计和实现
- [🔄] 基础组件和工具类开发
- [ ] 用户认证和个人中心
- [ ] 菜品展示功能
- [ ] 点餐核心功能
- [ ] 搜索和筛选功能
- [ ] 制作指导功能
- [ ] 界面美化和交互优化
- [ ] 性能优化和测试

## 快速开始

### 环境要求

- 微信开发者工具
- 微信小程序开发账号（可选，用于真机调试）

### 安装步骤

1. 克隆项目到本地
2. 使用微信开发者工具打开项目
3. 直接编译运行项目（无需额外配置）

### 配置说明

1. **项目配置**：
   - 在 `project.config.json` 中配置 `appid`（可选）
   - 本地开发无需云开发环境

2. **数据说明**：
   - 所有数据存储在本地
   - 用户数据保存在微信小程序本地存储中
   - 菜品数据来自 `miniprogram/data/dishes.js`

## 开发规范

### 代码规范
- 使用ES6+语法
- 遵循微信小程序开发规范
- 统一命名规范
- 添加必要注释

### 提交规范
- feat: 新功能
- fix: 修复问题
- docs: 文档更新
- style: 代码格式
- refactor: 重构
- test: 测试相关

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues：提交问题和建议
- 邮箱：[您的邮箱]

---

**注意**：本项目目前处于开发阶段，部分功能尚未完成。请关注项目进度更新。
