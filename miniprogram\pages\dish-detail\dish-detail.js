// pages/dish-detail/dish-detail.js
const { dishAPI, orderAPI } = require('../../utils/api');

Page({
  data: {
    dish: {},
    loading: true,
    dishId: ''
  },

  onLoad(options) {
    if (options.id) {
      this.setData({ dishId: options.id });
      this.loadDishDetail();
    }
  },

  async loadDishDetail() {
    try {
      this.setData({ loading: true });
      const dish = await dishAPI.getDishDetail(this.data.dishId);
      this.setData({ dish });
    } catch (error) {
      console.error('加载菜品详情失败:', error);
      wx.showToast({ title: '加载失败', icon: 'error' });
    } finally {
      this.setData({ loading: false });
    }
  },

  async onOrderTap() {
    try {
      await orderAPI.createOrder(this.data.dishId);
      wx.showToast({ title: '点餐成功', icon: 'success' });
    } catch (error) {
      console.error('点餐失败:', error);
      wx.showToast({ title: '点餐失败', icon: 'error' });
    }
  }
});