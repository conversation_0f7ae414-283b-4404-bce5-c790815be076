# 云开发环境配置指南

## 步骤1：开通云开发服务

### 1.1 在微信开发者工具中开通

1. 打开微信开发者工具
2. 打开项目后，点击工具栏上的"云开发"按钮
3. 如果是第一次使用，会提示开通云开发服务
4. 点击"开通"按钮
5. 选择付费方式（可以选择按量付费，有免费额度）
6. 创建环境，输入环境名称（如：family-ordering-dev）
7. 等待环境创建完成（通常需要几分钟）

### 1.2 获取环境ID

1. 环境创建完成后，在云开发控制台可以看到环境ID
2. 环境ID格式类似：`family-ordering-xxx`
3. 记录这个环境ID，后续配置需要用到

## 步骤2：配置项目中的环境ID

### 2.1 修改 app.js

```javascript
// miniprogram/app.js
App({
  onLaunch() {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        env: 'your-actual-env-id', // 替换为实际的环境ID
        traceUser: true,
      });
    }
    // ...
  }
  // ...
});
```

### 2.2 修改 constants.js

```javascript
// miniprogram/utils/constants.js
const APP_CONFIG = {
  CLOUD_ENV_ID: 'your-actual-env-id', // 替换为实际的环境ID
  // ...
};
```

## 步骤3：部署云函数

### 3.1 设置云函数环境

1. 在微信开发者工具中，右键点击 `cloudfunctions` 文件夹
2. 选择"更多设置" -> "设置"
3. 在弹出的设置窗口中，选择你创建的云开发环境
4. 点击"确定"

### 3.2 部署单个云函数

对于每个云函数文件夹，执行以下步骤：

1. 右键点击云函数文件夹（如 `initDatabase`）
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

需要部署的云函数列表：
- `initDatabase`
- `getDishes`
- `getDishDetail`
- `login`
- `createOrder`
- `getRecommendedDishes`

### 3.3 批量部署（可选）

1. 右键点击 `cloudfunctions` 文件夹
2. 选择"同步云函数列表"
3. 选择"上传所有云函数"

## 步骤4：初始化数据库

### 4.1 通过云开发控制台调用

1. 在微信开发者工具中点击"云开发"
2. 进入"云函数"页面
3. 找到 `initDatabase` 函数
4. 点击函数名称进入详情页
5. 点击"测试"按钮
6. 在测试参数中输入 `{}`
7. 点击"运行测试"
8. 查看返回结果，确认初始化成功

### 4.2 通过小程序代码调用

在小程序中添加临时代码：

```javascript
// 在某个页面的 onLoad 方法中添加
onLoad() {
  // 初始化数据库（只需要执行一次）
  wx.cloud.callFunction({
    name: 'initDatabase',
    success: (res) => {
      console.log('数据库初始化成功:', res);
      wx.showToast({
        title: '数据库初始化成功',
        icon: 'success'
      });
    },
    fail: (error) => {
      console.error('数据库初始化失败:', error);
      wx.showToast({
        title: '初始化失败',
        icon: 'error'
      });
    }
  });
}
```

## 步骤5：验证配置

### 5.1 检查云函数状态

1. 在云开发控制台的"云函数"页面
2. 确认所有云函数都显示为"部署成功"状态
3. 可以点击单个云函数查看详细信息和日志

### 5.2 检查数据库

1. 在云开发控制台的"数据库"页面
2. 确认创建了以下集合：
   - `dishes`（应该包含5条示例数据）
   - `users`（空集合）
   - `orders`（空集合）
   - `favorites`（空集合）

### 5.3 测试API调用

在小程序中测试基本功能：

```javascript
// 测试获取菜品列表
wx.cloud.callFunction({
  name: 'getDishes',
  data: {
    limit: 5
  },
  success: (res) => {
    console.log('获取菜品成功:', res);
  },
  fail: (error) => {
    console.error('获取菜品失败:', error);
  }
});
```

## 常见问题解决

### 问题1：errCode: -601034 没有权限

**原因：** 云开发环境未开通或环境ID配置错误

**解决方案：**
1. 确认已开通云开发服务
2. 检查环境ID配置是否正确
3. 确认云函数已正确部署

### 问题2：云函数调用超时

**原因：** 云函数未部署或部署失败

**解决方案：**
1. 重新部署云函数
2. 检查云函数代码是否有语法错误
3. 查看云函数日志

### 问题3：数据库连接失败

**原因：** 数据库权限设置或环境配置问题

**解决方案：**
1. 检查数据库权限设置
2. 确认环境ID配置正确
3. 重新初始化数据库

### 问题4：云函数执行失败

**原因：** 代码错误或依赖包问题

**解决方案：**
1. 查看云函数执行日志
2. 检查代码逻辑
3. 重新安装依赖包

## 调试技巧

### 1. 查看云函数日志

1. 在云开发控制台的"云函数"页面
2. 点击具体的云函数
3. 查看"调用日志"选项卡
4. 分析错误信息和执行过程

### 2. 使用控制台测试

1. 在云开发控制台直接测试云函数
2. 输入测试参数
3. 查看返回结果和日志

### 3. 本地调试

1. 使用 `console.log` 输出调试信息
2. 在微信开发者工具的控制台查看输出
3. 逐步排查问题

## 注意事项

1. **环境ID配置**：确保所有地方的环境ID都是一致的
2. **网络环境**：确保网络连接正常
3. **基础库版本**：确保小程序基础库版本支持云开发
4. **权限设置**：合理设置数据库权限
5. **资源监控**：关注云开发资源使用情况

## 完成检查清单

- [ ] 云开发服务已开通
- [ ] 环境ID已正确配置
- [ ] 所有云函数已部署成功
- [ ] 数据库已初始化
- [ ] 示例数据已创建
- [ ] API调用测试通过

完成以上步骤后，小程序应该可以正常调用云函数和访问数据库了。
