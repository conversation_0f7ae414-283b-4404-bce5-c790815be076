// 云函数：获取推荐菜品
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  try {
    const { limit = 10 } = event;
    const { OPENID } = cloud.getWXContext();

    // 获取用户偏好（如果已登录）
    let userPreferences = null;
    if (OPENID) {
      try {
        const userResult = await db.collection('users')
          .where({
            openid: OPENID
          })
          .get();
        
        if (userResult.data.length > 0) {
          userPreferences = userResult.data[0].preferences;
        }
      } catch (error) {
        console.log('获取用户偏好失败:', error);
      }
    }

    // 基础推荐算法：综合评分和点餐次数
    const result = await db.collection('dishes')
      .orderBy('rating', 'desc')
      .orderBy('orderCount', 'desc')
      .limit(limit * 2) // 获取更多数据用于筛选
      .get();

    let dishes = result.data;

    // 如果有用户偏好，进行个性化推荐
    if (userPreferences && userPreferences.favoriteCuisine.length > 0) {
      // 优先推荐用户喜欢的菜系
      const favoriteDishes = dishes.filter(dish => 
        userPreferences.favoriteCuisine.includes(dish.category)
      );
      
      const otherDishes = dishes.filter(dish => 
        !userPreferences.favoriteCuisine.includes(dish.category)
      );

      // 混合推荐：70%喜欢的菜系，30%其他菜系
      const favoriteCount = Math.ceil(limit * 0.7);
      const otherCount = limit - favoriteCount;

      dishes = [
        ...favoriteDishes.slice(0, favoriteCount),
        ...otherDishes.slice(0, otherCount)
      ];
    } else {
      // 没有用户偏好时，按评分和热度推荐
      dishes = dishes.slice(0, limit);
    }

    // 添加推荐理由
    dishes = dishes.map(dish => ({
      ...dish,
      recommendReason: getRecommendReason(dish, userPreferences)
    }));

    return {
      code: 0,
      message: '获取推荐菜品成功',
      data: dishes
    };

  } catch (error) {
    console.error('获取推荐菜品失败:', error);
    return {
      code: -1,
      message: '获取推荐菜品失败',
      error: error.message
    };
  }
};

// 生成推荐理由
function getRecommendReason(dish, userPreferences) {
  const reasons = [];

  if (dish.rating >= 4.5) {
    reasons.push('高评分');
  }

  if (dish.orderCount >= 100) {
    reasons.push('热门菜品');
  }

  if (dish.difficulty <= 2) {
    reasons.push('简单易做');
  }

  if (dish.cookTime <= 30) {
    reasons.push('快手菜');
  }

  if (userPreferences && userPreferences.favoriteCuisine.includes(dish.category)) {
    reasons.push('符合口味偏好');
  }

  return reasons.length > 0 ? reasons.join('、') : '为您推荐';
}
