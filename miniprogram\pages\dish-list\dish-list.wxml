<!--pages/dish-list/dish-list.wxml-->
<view class="page-container">
  <!-- 筛选栏 -->
  <view class="filter-bar">
    <view class="filter-item" bindtap="onSortTap">
      <text>{{currentSort.name}}</text>
      <text class="filter-arrow">▼</text>
    </view>
    <view class="filter-item" bindtap="onFilterTap">
      <text>筛选</text>
      <text class="filter-arrow">▼</text>
    </view>
  </view>

  <!-- 菜品列表 -->
  <view class="dish-list">
    <view
      class="dish-item"
      wx:for="{{dishes}}"
      wx:key="_id"
      bindtap="onDishTap"
      data-id="{{item._id}}"
    >
      <image class="dish-image" src="{{item.image}}" mode="aspectFill" lazy-load="true"></image>
      <view class="dish-info">
        <text class="dish-name">{{item.name}}</text>
        <text class="dish-desc">{{item.description}}</text>
        <view class="dish-meta">
          <text class="meta-item">⏱️ {{item.cookTime}}分钟</text>
          <text class="meta-item">{{item.difficultyText}}</text>
          <text class="meta-item">⭐ {{item.rating}}</text>
        </view>
        <view class="dish-tags">
          <text class="tag" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
        </view>
      </view>
      <view class="dish-actions">
        <button class="btn-order" bindtap="onOrderTap" data-id="{{item._id}}" catchtap="true">点餐</button>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="loading-more" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!hasMore && dishes.length > 0}}">
    <text>没有更多数据了</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{isEmpty}}">
    <text class="empty-icon">🍽️</text>
    <text class="empty-text">暂无菜品</text>
  </view>
</view>