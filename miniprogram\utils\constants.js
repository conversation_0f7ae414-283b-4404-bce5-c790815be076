// utils/constants.js - 常量配置文件

/**
 * 应用配置
 */
const APP_CONFIG = {
  // 应用名称
  APP_NAME: '家庭点餐',
  
  // 版本号
  VERSION: '1.0.0',
  
  // 云开发环境ID
  CLOUD_ENV_ID: 'your-cloud-env-id',
  
  // 分页配置
  PAGE_SIZE: 20,
  
  // 图片配置
  IMAGE_MAX_SIZE: 5 * 1024 * 1024, // 5MB
  IMAGE_ALLOWED_TYPES: ['jpg', 'jpeg', 'png', 'webp'],
  
  // 缓存配置
  CACHE_EXPIRE_TIME: 30 * 60 * 1000, // 30分钟
  
  // 请求超时时间
  REQUEST_TIMEOUT: 10000, // 10秒
};

/**
 * 菜品分类
 */
const DISH_CATEGORIES = [
  { id: 'all', name: '全部', icon: '🍽️' },
  { id: 'sichuan', name: '川菜', icon: '🌶️' },
  { id: 'cantonese', name: '粤菜', icon: '🦐' },
  { id: 'shandong', name: '鲁菜', icon: '🐟' },
  { id: 'jiangsu', name: '苏菜', icon: '🦆' },
  { id: 'zhejiang', name: '浙菜', icon: '🐟' },
  { id: 'fujian', name: '闽菜', icon: '🦀' },
  { id: 'hunan', name: '湘菜', icon: '🌶️' },
  { id: 'anhui', name: '徽菜', icon: '🐷' },
  { id: 'soup', name: '汤品', icon: '🍲' },
  { id: 'dessert', name: '甜品', icon: '🍰' },
  { id: 'snack', name: '小食', icon: '🥟' }
];

/**
 * 菜品难度等级
 */
const DIFFICULTY_LEVELS = [
  { level: 1, name: '简单', color: '#52C41A', icon: '⭐' },
  { level: 2, name: '容易', color: '#73D13D', icon: '⭐⭐' },
  { level: 3, name: '中等', color: '#FAAD14', icon: '⭐⭐⭐' },
  { level: 4, name: '困难', color: '#FF7A45', icon: '⭐⭐⭐⭐' },
  { level: 5, name: '专家', color: '#FF4D4F', icon: '⭐⭐⭐⭐⭐' }
];

/**
 * 点餐状态
 */
const ORDER_STATUS = {
  PENDING: 'pending',
  COOKING: 'cooking',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

/**
 * 点餐状态显示配置
 */
const ORDER_STATUS_CONFIG = {
  [ORDER_STATUS.PENDING]: {
    name: '待制作',
    color: '#FAAD14',
    icon: '⏳'
  },
  [ORDER_STATUS.COOKING]: {
    name: '制作中',
    color: '#1890FF',
    icon: '👨‍🍳'
  },
  [ORDER_STATUS.COMPLETED]: {
    name: '已完成',
    color: '#52C41A',
    icon: '✅'
  },
  [ORDER_STATUS.CANCELLED]: {
    name: '已取消',
    color: '#FF4D4F',
    icon: '❌'
  }
};

/**
 * 用户角色
 */
const USER_ROLES = {
  MEMBER: 'member',
  ADMIN: 'admin'
};

/**
 * 用户角色配置
 */
const USER_ROLE_CONFIG = {
  [USER_ROLES.MEMBER]: {
    name: '家庭成员',
    permissions: ['view', 'order', 'favorite']
  },
  [USER_ROLES.ADMIN]: {
    name: '管理员',
    permissions: ['view', 'order', 'favorite', 'manage', 'edit']
  }
};

/**
 * 排序选项
 */
const SORT_OPTIONS = [
  { key: 'default', name: '默认排序' },
  { key: 'rating', name: '评分最高' },
  { key: 'orderCount', name: '最受欢迎' },
  { key: 'createTime', name: '最新添加' },
  { key: 'cookTime', name: '制作时间' },
  { key: 'difficulty', name: '难度等级' }
];

/**
 * 筛选选项
 */
const FILTER_OPTIONS = {
  // 制作时间筛选
  cookTime: [
    { key: 'all', name: '全部时间' },
    { key: '0-30', name: '30分钟内' },
    { key: '30-60', name: '30-60分钟' },
    { key: '60-120', name: '1-2小时' },
    { key: '120+', name: '2小时以上' }
  ],
  
  // 难度筛选
  difficulty: [
    { key: 'all', name: '全部难度' },
    { key: '1-2', name: '简单' },
    { key: '3', name: '中等' },
    { key: '4-5', name: '困难' }
  ],
  
  // 口味筛选
  taste: [
    { key: 'all', name: '全部口味' },
    { key: 'sweet', name: '甜味' },
    { key: 'sour', name: '酸味' },
    { key: 'spicy', name: '辣味' },
    { key: 'salty', name: '咸味' },
    { key: 'bitter', name: '苦味' },
    { key: 'umami', name: '鲜味' }
  ]
};

/**
 * 本地存储键名
 */
const STORAGE_KEYS = {
  USER_INFO: 'userInfo',
  SEARCH_HISTORY: 'searchHistory',
  DISH_CACHE: 'dishCache',
  SETTINGS: 'settings',
  FAVORITES_CACHE: 'favoritesCache',
  ORDER_CACHE: 'orderCache'
};

/**
 * 事件名称
 */
const EVENT_NAMES = {
  USER_LOGIN: 'userLogin',
  USER_LOGOUT: 'userLogout',
  ORDER_CREATED: 'orderCreated',
  ORDER_UPDATED: 'orderUpdated',
  FAVORITE_ADDED: 'favoriteAdded',
  FAVORITE_REMOVED: 'favoriteRemoved',
  DISH_VIEWED: 'dishViewed'
};

/**
 * 错误码
 */
const ERROR_CODES = {
  SUCCESS: 0,
  UNKNOWN_ERROR: -1,
  NETWORK_ERROR: -2,
  AUTH_ERROR: -3,
  PERMISSION_ERROR: -4,
  VALIDATION_ERROR: -5,
  NOT_FOUND: -6,
  ALREADY_EXISTS: -7,
  RATE_LIMIT: -8
};

/**
 * 错误消息
 */
const ERROR_MESSAGES = {
  [ERROR_CODES.UNKNOWN_ERROR]: '未知错误',
  [ERROR_CODES.NETWORK_ERROR]: '网络连接失败',
  [ERROR_CODES.AUTH_ERROR]: '身份验证失败',
  [ERROR_CODES.PERMISSION_ERROR]: '权限不足',
  [ERROR_CODES.VALIDATION_ERROR]: '数据验证失败',
  [ERROR_CODES.NOT_FOUND]: '资源不存在',
  [ERROR_CODES.ALREADY_EXISTS]: '资源已存在',
  [ERROR_CODES.RATE_LIMIT]: '请求过于频繁'
};

/**
 * 默认设置
 */
const DEFAULT_SETTINGS = {
  // 通知设置
  notifications: {
    orderReminder: true,
    newDishAlert: true,
    ratingReminder: true
  },
  
  // 显示设置
  display: {
    theme: 'light',
    fontSize: 'medium',
    showImages: true,
    showDifficulty: true
  },
  
  // 隐私设置
  privacy: {
    shareOrderHistory: false,
    showInFamily: true
  }
};

/**
 * 正则表达式
 */
const REGEX_PATTERNS = {
  // 手机号
  PHONE: /^1[3-9]\d{9}$/,
  
  // 邮箱
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  
  // 中文字符
  CHINESE: /[\u4e00-\u9fa5]/,
  
  // 数字
  NUMBER: /^\d+$/,
  
  // 小数
  DECIMAL: /^\d+(\.\d+)?$/
};

module.exports = {
  APP_CONFIG,
  DISH_CATEGORIES,
  DIFFICULTY_LEVELS,
  ORDER_STATUS,
  ORDER_STATUS_CONFIG,
  USER_ROLES,
  USER_ROLE_CONFIG,
  SORT_OPTIONS,
  FILTER_OPTIONS,
  STORAGE_KEYS,
  EVENT_NAMES,
  ERROR_CODES,
  ERROR_MESSAGES,
  DEFAULT_SETTINGS,
  REGEX_PATTERNS
};
