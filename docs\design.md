# 家庭点餐微信小程序设计方案

## 1. 系统架构设计

### 1.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小程序端   │    │    云开发后端    │    │    数据存储     │
│                │    │                │    │                │
│  - 用户界面     │◄──►│  - 云函数       │◄──►│  - 云数据库     │
│  - 业务逻辑     │    │  - API接口      │    │  - 云存储       │
│  - 本地缓存     │    │  - 权限控制     │    │  - 静态资源     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.2 技术栈选择
- **前端**：微信小程序原生开发 + WeUI组件库
- **后端**：微信云开发（云函数 + 云数据库）
- **存储**：云数据库 + 云存储
- **开发工具**：微信开发者工具

## 2. 数据库设计

### 2.1 数据表结构

#### 2.1.1 菜品表 (dishes)
```json
{
  "_id": "dish_id",
  "name": "菜品名称",
  "category": "菜品分类",
  "image": "菜品图片URL",
  "description": "菜品描述",
  "difficulty": 1-5, // 难度等级
  "cookTime": 30, // 制作时间(分钟)
  "ingredients": [
    {
      "name": "食材名称",
      "amount": "用量",
      "unit": "单位"
    }
  ],
  "steps": [
    {
      "stepNumber": 1,
      "description": "制作步骤描述",
      "image": "步骤图片URL(可选)"
    }
  ],
  "tags": ["标签1", "标签2"],
  "nutrition": {
    "calories": "热量",
    "protein": "蛋白质",
    "carbs": "碳水化合物"
  },
  "rating": 4.5, // 平均评分
  "orderCount": 0, // 点餐次数
  "createTime": "创建时间",
  "updateTime": "更新时间"
}
```

#### 2.1.2 用户表 (users)
```json
{
  "_id": "user_id",
  "openid": "微信openid",
  "nickname": "用户昵称",
  "avatar": "头像URL",
  "familyId": "家庭ID",
  "role": "member|admin", // 角色
  "preferences": {
    "spicyLevel": 1-5, // 辣度偏好
    "sweetLevel": 1-5, // 甜度偏好
    "favoriteCuisine": ["川菜", "粤菜"] // 喜欢的菜系
  },
  "createTime": "创建时间",
  "lastLoginTime": "最后登录时间"
}
```

#### 2.1.3 点餐记录表 (orders)
```json
{
  "_id": "order_id",
  "userId": "用户ID",
  "dishId": "菜品ID",
  "dishName": "菜品名称",
  "orderTime": "点餐时间",
  "status": "pending|cooking|completed", // 状态
  "rating": 1-5, // 用户评分
  "comment": "评价内容",
  "cookTime": "实际制作时间"
}
```

#### 2.1.4 收藏表 (favorites)
```json
{
  "_id": "favorite_id",
  "userId": "用户ID",
  "dishId": "菜品ID",
  "createTime": "收藏时间"
}
```

### 2.2 数据关系
- 用户与点餐记录：一对多
- 用户与收藏：一对多
- 菜品与点餐记录：一对多
- 菜品与收藏：一对多

## 3. 页面结构设计

### 3.1 页面层级结构
```
首页 (index)
├── 菜品列表页 (dish-list)
│   ├── 菜品详情页 (dish-detail)
│   └── 制作步骤页 (cooking-steps)
├── 点餐记录页 (order-history)
├── 收藏页面 (favorites)
├── 搜索页面 (search)
└── 个人中心 (profile)
    ├── 个人设置 (settings)
    └── 家庭管理 (family)
```

### 3.2 页面功能说明

#### 3.2.1 首页 (index)
- 展示推荐菜品
- 快速搜索入口
- 分类导航
- 今日推荐

#### 3.2.2 菜品列表页 (dish-list)
- 菜品网格展示
- 分类筛选
- 排序功能
- 无限滚动加载

#### 3.2.3 菜品详情页 (dish-detail)
- 菜品基本信息
- 食材清单
- 制作步骤预览
- 点餐按钮
- 收藏功能
- 评价展示

#### 3.2.4 制作步骤页 (cooking-steps)
- 分步骤展示制作方法
- 计时器功能
- 步骤图片展示
- 制作笔记

## 4. 用户界面设计

### 4.1 设计原则
- **简洁性**：界面简洁，突出核心功能
- **一致性**：保持设计风格统一
- **易用性**：操作简单直观
- **美观性**：符合现代审美

### 4.2 色彩方案
- **主色调**：#FF6B35 (橙色，代表食物的温暖)
- **辅助色**：#4ECDC4 (青色，代表清新)
- **背景色**：#F8F9FA (浅灰色)
- **文字色**：#333333 (深灰色)

### 4.3 组件设计
- **菜品卡片**：包含图片、名称、难度、时间
- **步骤卡片**：包含步骤号、描述、图片
- **评分组件**：星级评分显示
- **标签组件**：菜品标签展示

## 5. 云函数设计

### 5.1 云函数列表
- `getDishes`: 获取菜品列表
- `getDishDetail`: 获取菜品详情
- `createOrder`: 创建点餐记录
- `getUserOrders`: 获取用户点餐记录
- `addToFavorites`: 添加收藏
- `searchDishes`: 搜索菜品
- `updateUserProfile`: 更新用户信息

### 5.2 API接口设计

#### 5.2.1 获取菜品列表
```javascript
// 请求
{
  "category": "分类(可选)",
  "page": 1,
  "limit": 20,
  "sortBy": "rating|orderCount|createTime"
}

// 响应
{
  "code": 0,
  "data": {
    "dishes": [...],
    "total": 100,
    "hasMore": true
  }
}
```

#### 5.2.2 创建点餐记录
```javascript
// 请求
{
  "dishId": "菜品ID",
  "note": "备注(可选)"
}

// 响应
{
  "code": 0,
  "data": {
    "orderId": "点餐记录ID"
  }
}
```

## 6. 性能优化方案

### 6.1 前端优化
- **图片懒加载**：使用小程序原生懒加载
- **数据缓存**：缓存常用数据到本地存储
- **分页加载**：列表数据分页加载
- **代码分包**：按功能模块分包

### 6.2 后端优化
- **数据库索引**：为常用查询字段建立索引
- **缓存策略**：热门数据缓存
- **图片压缩**：自动压缩上传的图片
- **CDN加速**：静态资源使用CDN

## 7. 安全设计

### 7.1 数据安全
- **权限控制**：基于用户角色的权限控制
- **数据校验**：前后端数据校验
- **敏感信息**：敏感信息加密存储

### 7.2 接口安全
- **身份验证**：基于微信登录的身份验证
- **请求限制**：API请求频率限制
- **数据过滤**：输入数据过滤和清理

## 8. 部署方案

### 8.1 开发环境
- 使用微信开发者工具本地开发
- 云开发测试环境

### 8.2 生产环境
- 微信小程序正式版发布
- 云开发正式环境
- 域名配置和SSL证书

## 9. 监控和维护

### 9.1 性能监控
- 小程序性能监控
- 云函数执行监控
- 数据库性能监控

### 9.2 错误监控
- 前端错误收集
- 后端异常监控
- 用户反馈收集

### 9.3 数据备份
- 定期数据备份
- 灾难恢复方案
- 数据迁移计划
