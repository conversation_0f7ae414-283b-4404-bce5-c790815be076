/* pages/index/index.wxss */

.search-bar {
  padding: 20rpx;
  background-color: #FFFFFF;
  margin-bottom: 20rpx;
}

.search-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #F8F9FA;
  border-radius: 50rpx;
  color: #999999;
}

.search-placeholder {
  flex: 1;
}

.search-icon {
  font-size: 32rpx;
}

.category-nav {
  background-color: #FFFFFF;
  margin-bottom: 20rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: inline-flex;
  padding: 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
  padding: 20rpx;
  border-radius: 16rpx;
  min-width: 120rpx;
  transition: all 0.3s ease;
}

.category-item.active {
  background-color: #FF6B35;
  color: #FFFFFF;
}

.category-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.category-name {
  font-size: 24rpx;
}

.section {
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx 20rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.section-more {
  font-size: 28rpx;
  color: #FF6B35;
}

.dish-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0 10rpx;
}

.dish-card {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.dish-image {
  width: 100%;
  height: 200rpx;
}

.dish-info {
  padding: 20rpx;
}

.dish-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.dish-meta {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.dish-rating {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.rating-stars {
  margin-right: 10rpx;
}

.dish-list {
  padding: 0 20rpx;
}

.dish-item {
  display: flex;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.dish-thumb {
  width: 200rpx;
  height: 150rpx;
  flex-shrink: 0;
}

.dish-content {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.dish-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.dish-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dish-stats {
  display: flex;
  font-size: 24rpx;
  color: #999999;
}

.stat-item {
  margin-right: 20rpx;
}

.dish-date {
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
}

.loading-more {
  text-align: center;
  padding: 40rpx;
  color: #999999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999999;
  margin-bottom: 40rpx;
}
